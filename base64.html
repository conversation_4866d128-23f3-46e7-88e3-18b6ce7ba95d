<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta property="og:title" content="Base64 Encoder/Decoder">
  <meta property="og:description" content="Kodiert und dekodiert Texte im Base64-Format.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://tools.nyanya.de/base64">
  <meta property="og:image" content="/assets/og-image.png">
  <title>Base64 Encoder/Decoder</title>
  <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css" 
      integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a" 
      crossorigin="anonymous">
  <style>
    :root {
      --text-color: #333333;
      --background-color: #F5F5F7;
      --primary-color: #007AFF;
      --secondary-color: #FFFFFF;
      --border-color: #E5E5E5;
      --container-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
      --danger-color: #FF3B30;
      --success-color: #34C759;
    }
    
    @media (prefers-color-scheme: dark) {
      :root {
        --text-color: #F5F5F7;
        --background-color: #1C1C1E;
        --primary-color: #0A84FF;
        --secondary-color: #2C2C2E;
        --border-color: #3A3A3C;
        --container-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
        --danger-color: #FF453A;
        --success-color: #30D158;
      }
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      color: var(--text-color);
      background-color: var(--background-color);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.5;
      margin: 0;
      padding: 0;
      transition: background-color 0.3s ease;
      background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.05), transparent);
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      text-align: center;
      padding: 20px 0;
      margin-bottom: 20px;
    }
    
    h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    h2 {
      font-size: 20px;
      font-weight: 500;
      margin: 20px 0;
    }
    
    p {
      margin-bottom: 20px;
    }
    
    .card {
      background-color: var(--secondary-color);
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: var(--container-shadow);
      transition: all 0.3s ease;
      border: 1px solid var(--border-color);
    }
    
    .input-group, .output-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    textarea {
      width: 100%;
      min-height: 150px;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid var(--border-color);
      background-color: var(--background-color);
      color: var(--text-color);
      font-family: inherit;
      font-size: 16px;
      resize: vertical;
      transition: border-color 0.3s;
    }
    
    textarea:focus {
      outline: none;
      border-color: var(--primary-color);
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin: 20px 0;
    }
    
    button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 20px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: color-mix(in srgb, var(--primary-color) 85%, black);
    }
    
    button.secondary {
      background-color: var(--secondary-color);
      color: var(--text-color);
      border: 1px solid var(--border-color);
    }
    
    button.secondary:hover {
      background-color: color-mix(in srgb, var(--secondary-color) 90%, black);
    }
    
    .copy-button {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .alert {
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
    }
    
    .alert.success {
      background-color: var(--success-color);
      color: white;
    }
    
    .alert.error {
      background-color: var(--danger-color);
      color: white;
    }
    
    @media (max-width: 768px) {
      .button-group {
        flex-direction: column;
      }
    }
    
    .icon {
      margin-right: 8px;
    }
    
    .title-icon {
      margin-right: 10px;
      color: var(--text-color);
    }

    .back-button {
        margin-top: 24px;
        text-align: center;
        width: 100%;
    }

    .back-button a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 8px;
        transition: background-color 0.3s ease;
    }

    .back-button a:hover {
        background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1><i class="title-icon fa-solid fa-code"></i>Base64 Encoder/Decoder</h1>
    </header>
    
    <main>
      <div class="card">
        <div class="input-group">
          <label for="input-text">Eingabetext:</label>
          <textarea id="input-text" placeholder="Gib hier den Text ein, den du kodieren oder dekodieren möchtest..."></textarea>
        </div>
        
        <div class="button-group">
          <button id="encode-text-btn"><i class="icon fa-solid fa-lock"></i>Kodieren</button>
          <button id="decode-text-btn"><i class="icon fa-solid fa-unlock"></i>Dekodieren</button>
          <button id="clear-text-btn" class="secondary"><i class="icon fa-solid fa-trash"></i>Löschen</button>
        </div>
        
        <div class="alert" id="text-alert"></div>
        
        <div class="output-group">
          <label for="output-text">Ergebnis:</label>
          <textarea id="output-text" readonly></textarea>
        </div>
        
        <div class="button-group">
          <button id="copy-text-btn" class="copy-button">
            <i class="icon fa-solid fa-copy"></i><span>Kopieren</span>
          </button>
        </div>
      </div>
    </main>
    <div class="back-button">
        <a href="index.html">← Zurück zur Übersicht</a>
    </div>
  </div>
  
  <script>
    // Text encoding/decoding
    const inputText = document.getElementById('input-text');
    const outputText = document.getElementById('output-text');
    const encodeTextBtn = document.getElementById('encode-text-btn');
    const decodeTextBtn = document.getElementById('decode-text-btn');
    const clearTextBtn = document.getElementById('clear-text-btn');
    const copyTextBtn = document.getElementById('copy-text-btn');
    const textAlert = document.getElementById('text-alert');
    
    function showAlert(alertElement, message, type) {
      // Nur Fehler anzeigen, Erfolge werden ignoriert
      if (type === 'error') {
        alertElement.textContent = message;
        alertElement.className = `alert ${type}`;
        alertElement.style.display = 'block';
        // Kein setTimeout mehr, damit die Fehlermeldung permanent angezeigt wird
      } else {
        // Bei Erfolg nur die vorherige Fehlermeldung entfernen falls vorhanden
        alertElement.style.display = 'none';
      }
    }
    
    encodeTextBtn.addEventListener('click', () => {
      try {
        const text = inputText.value;
        if (!text) {
          showAlert(textAlert, 'Bitte gib einen Text ein', 'error');
          outputText.value = ''; // Ergebnisfeld leeren bei Fehler
          return;
        }
        
        const encoded = btoa(unescape(encodeURIComponent(text)));
        outputText.value = encoded;
        showAlert(textAlert, '', 'success'); // Erfolg ohne Nachricht, entfernt nur alte Fehler
      } catch (error) {
        showAlert(textAlert, 'Fehler beim Kodieren: ' + error.message, 'error');
        outputText.value = ''; // Ergebnisfeld leeren bei Fehler
      }
    });
    
    decodeTextBtn.addEventListener('click', () => {
      try {
        const text = inputText.value;
        if (!text) {
          showAlert(textAlert, 'Bitte gib einen Base64-kodierten Text ein', 'error');
          outputText.value = ''; // Ergebnisfeld leeren bei Fehler
          return;
        }
        
        const decoded = decodeURIComponent(escape(atob(text)));
        outputText.value = decoded;
        showAlert(textAlert, '', 'success'); // Erfolg ohne Nachricht, entfernt nur alte Fehler
      } catch (error) {
        showAlert(textAlert, 'Fehler beim Dekodieren. Ist der Text korrekt Base64-kodiert?', 'error');
        outputText.value = ''; // Ergebnisfeld leeren bei Fehler
      }
    });
    
    clearTextBtn.addEventListener('click', () => {
      inputText.value = '';
      outputText.value = '';
      showAlert(textAlert, '', 'success'); // Fehlermeldung entfernen
    });
    
    copyTextBtn.addEventListener('click', () => {
      if (!outputText.value) {
        showAlert(textAlert, 'Nichts zum Kopieren!', 'error');
        return;
      }
      
      outputText.select();
      document.execCommand('copy');
    });

    // Moderne Clipboard API wenn verfügbar
    if (navigator.clipboard) {
      copyTextBtn.addEventListener('click', async () => {
        if (!outputText.value) {
          showAlert(textAlert, 'Nichts zum Kopieren!', 'error');
          return;
        }
        
        try {
          await navigator.clipboard.writeText(outputText.value);
        } catch (err) {
          // Fallback zur älteren Methode
          outputText.select();
          document.execCommand('copy');
        }
      });
    }
  </script>
</body>
</html> 
