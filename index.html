<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="NyaNya Tools">
    <meta property="og:description" content="Eine Sammlung nützlicher Tools für verschiedene Aufgaben.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/">
    <meta property="og:image" content="/assets/og-image.png">
    <title>NyaNya Tools</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css" 
          integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a" 
          crossorigin="anonymous">
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 16px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --card-padding: 24px;
            --transition: all 0.3s ease;
            --accent-color: #5E5CE6;
            --success-color: #34C759;
            --card-hover-transform: translateY(-5px);
            --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            --info-value-bg: rgba(0, 0, 0, 0.02);
            --border-color: rgba(0, 0, 0, 0.05);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                --accent-color: #5E5CE6;
                --success-color: #30D158;
                --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
                --info-value-bg: rgba(255, 255, 255, 0.05);
                --border-color: rgba(255, 255, 255, 0.1);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            margin-bottom: 40px;
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .subtitle {
            font-size: 1.2rem;
            color: var(--secondary-text-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .tool-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: var(--card-padding);
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            height: 100%;
            text-decoration: none;
            color: var(--text-color);
        }

        .tool-card:hover {
            transform: var(--card-hover-transform);
            box-shadow: var(--card-hover-shadow);
        }

        .tool-card h2 {
            margin-bottom: 10px;
            color: var(--primary-color);
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }

        .tool-icon {
            margin-right: 10px;
            color: var(--primary-color);
            font-size: 1.5rem;
            width: 32px;
            text-align: center;
        }

        .tool-card p {
            margin-bottom: 16px;
            color: var(--secondary-text-color);
            flex-grow: 1;
            font-size: 1rem;
            line-height: 1.5;
        }

        .tool-button {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            text-align: center;
            margin-top: auto;
        }

        .tool-card:hover .tool-button {
            opacity: 0.9;
        }

        footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px 0;
            color: var(--secondary-text-color);
            font-size: 0.9rem;
        }

        .mr-2 {
            margin-right: 10px;
        }

        .mb-2 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fa-solid fa-cat mr-2"></i>NyaNya Tools</h1>
            <p class="subtitle">Eine Sammlung nützlicher Tools für verschiedene Aufgaben.</p>
        </header>

        <div class="tools-grid">
            <a href="/2048.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-gamepad"></i>2048</h2>
                <p>Spiele den Klassiker 2048 - Verbinde die Zahlen und versuche, die 2048-Kachel zu erreichen!</p>
                <span class="tool-button">Spiel starten</span>
            </a>
            
            <a href="/base64.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-code"></i>Base64 Encoder/Decoder</h2>
                <p>Kodiert und dekodiert Texte im Base64-Format.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/clipboard-viewer.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-clipboard"></i>Clipboard Viewer</h2>
                <p>Analysiert den Inhalt der Zwischenablage und zeigt die verschiedenen Datenformate an.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/counter.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-arrow-up-9-1"></i>Counter</h2>
                <p>Ein einfacher Zähler zum Hoch- und Runterzählen.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/decimal-converter.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-calculator"></i>Zahlensysteme-Konverter</h2>
                <p>Konvertiert Dezimalzahlen in andere Zahlensysteme wie Hexadezimal und Binär.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/doodle-jump.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-gamepad"></i>Doodle Jump</h2>
                <p>Springe auf Plattformen und erreiche neue Höhen!!</p>
                <span class="tool-button">Spiel starten</span>
            </a>

            <a href="/extract-urls.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-link"></i>Extract URLs</h2>
                <p>Extrahiert alle URLs aus einem Text und listet sie übersichtlich auf.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/favicon-tester.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-image"></i>Favicon-Tester</h2>
                <p>Testet Favicons in verschiedenen Größen und Formaten auf deine Website.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/file-hash-calculator.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-fingerprint"></i>File Hash Calculator</h2>
                <p>Berechnet Datei-Hashes (MD5, SHA-1, SHA-256).</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/ff-ext-checker.html" class="tool-card">
                <h2><i class="tool-icon fa-brands fa-firefox"></i>Firefox Extension Checker</h2>
                <p>Zeigt Infos über Firefox-Erweiterungen aus einer extensions.json-Datei an.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/github-changelog.html" class="tool-card">
                <h2><i class="tool-icon fa-brands fa-github"></i>GitHub Changelog</h2>
                <p>Zeigt Changelogs von GitHub Releases an und ermöglicht das einfache Kopieren.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/ics-generator.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-calendar-plus"></i>ICS Generator</h2>
                <p>Erstellt Kalenderereignisse im iCalendar (ICS) Format zum Import in Kalender-Apps.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/image-cropper.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-crop"></i>Image Cropper</h2>
                <p>Einfaches Tool zum Zuschneiden von Bildern im Browser.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/image-to-data-uri.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-image"></i>Image to Data URI</h2>
                <p>Konvertiert Bilder in Data-URI-Format zur direkten Einbettung in Websites oder CSS.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/json-formatter.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-code"></i>JSON Formatter</h2>
                <p>Formatiert, validiert und verschönert JSON-Daten mit Syntax-Highlighting.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/qr-code-generator.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-qrcode"></i>QR Code Generator</h2>
                <p>Erstellt QR Codes für eingegebenen Text oder URLs.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/qr-decoder.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-qrcode"></i>QR-Code Decoder</h2>
                <p>Dekodiert QR-Codes aus Bildern.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/system-info.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-laptop"></i>System Info</h2>
                <p>Zeigt detaillierte Informationen zu deinem Betriebssystem und Browser an.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/text-reverser.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-arrow-right-arrow-left"></i>Text Reverser</h2>
                <p>Kehrt eingegebenen Text um - von vorne nach hinten oder umgekehrt.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/xml-formatter.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-code"></i>XML Formatter</h2>
                <p>Formatiert, validiert und verschönert XML-Dokumente mit Syntax-Highlighting.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/currency-converter.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-money-bill-transfer"></i>Währungsumrechner</h2>
                <p>Rechne Beträge zwischen verschiedenen Währungen mit aktuellen Wechselkursen um.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/utm-builder.html" class="tool-card">
                <h2><i class="tool-icon fa-solid fa-tags"></i>UTM Campaign URL Builder</h2>
                <p>Erstellt URLs mit UTM-Parametern zur Verfolgung von Marketingkampagnen.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>

            <a href="/yt-thumbs.html" class="tool-card">
                <h2><i class="tool-icon fa-brands fa-youtube"></i>YouTube Thumbnails</h2>
                <p>Extrahiert Thumbnails verschiedener Qualitätsstufen aus YouTube-Video-URLs.</p>
                <span class="tool-button">Tool öffnen</span>
            </a>
        </div>

        <footer>
            <p class="mb-2">Interesse an noch mehr Tools? Schau mal bei <a href="https://tools.ponywave.de/" target="_blank">PonyWave Tools</a> vorbei!</p>
            <p>Alle Tools wurden mit <i class="fa-solid fa-heart" style="color: #FF3B30;"></i> per KI <i class="fa-solid fa-robot"></i> erstellt</p>
        </footer>
    </div>
</body>
</html>
