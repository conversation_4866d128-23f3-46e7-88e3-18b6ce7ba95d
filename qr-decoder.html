<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="QR-Code Decoder">
    <meta property="og:description" content="Dekodiert QR-Codes aus Bildern (Hochladen, Drag&Drop, Einfügen).">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/qr-decoder">
    <meta property="og:image" content="/assets/og-image.png">
    <title>QR-Code Decoder</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css" 
        integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a" 
        crossorigin="anonymous">
    <script src="/assets/jsQR.min.js"></script>
    <style>
        :root {
            --text-color: #333333;
            --background-color: #F5F5F7;
            --primary-color: #007AFF;
            --secondary-color: #FFFFFF;
            --border-color: #E5E5E5;
            --container-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --danger-color: #FF3B30;
            --success-color: #34C759;
            --drop-zone-bg: #F9F9F9;
            --drop-zone-border: #CCCCCC;
            --drop-zone-text: #666666;
            --border-radius: 8px;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #F5F5F7;
                --background-color: #1C1C1E;
                --primary-color: #0A84FF;
                --secondary-color: #2C2C2E;
                --border-color: #3A3A3C;
                --container-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                --danger-color: #FF453A;
                --success-color: #30D158;
                --drop-zone-bg: #2C2C2E;
                --drop-zone-border: #555555;
                --drop-zone-text: #AAAAAA;
            }
        }

        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }
        
        body {
            color: var(--text-color);
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            transition: background-color 0.3s ease;
            background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.05), transparent);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 20px;
            font-weight: 500;
            margin: 20px 0;
        }

        .card {
            background-color: var(--secondary-color);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--container-shadow);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        #drop-zone {
            border: 2px dashed var(--drop-zone-border);
            border-radius: var(--border-radius);
            padding: 40px;
            text-align: center;
            cursor: pointer;
            background-color: var(--drop-zone-bg);
            color: var(--drop-zone-text);
            width: 100%;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        #drop-zone.dragover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
            border-color: var(--primary-color);
        }
        
        #file-input {
            display: none;
        }

        .result-container {
            margin-top: 20px;
            width: 100%;
        }

        #result {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
            resize: vertical;
            transition: border-color 0.3s;
        }

        #result:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        
        .alert.success {
            background-color: var(--success-color);
            color: white;
            display: none;
        }
        
        .alert.error {
            background-color: var(--danger-color);
            color: white;
            display: none;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
        }
        
        button.secondary {
            background-color: var(--secondary-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        button.secondary:hover {
            background-color: color-mix(in srgb, var(--secondary-color) 90%, black);
        }

        .copy-button {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        #qr-canvas {
            display: none;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }

        .icon {
            margin-right: 8px;
        }
        
        .title-icon {
            margin-right: 10px;
            color: var(--text-color);
        }

        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
            #drop-zone {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="title-icon fa-solid fa-qrcode"></i>QR-Code Decoder</h1>
        </header>

        <main>
            <div class="card">
                <div id="drop-zone">
                    <i class="fa-solid fa-upload fa-2x" style="margin-bottom: 10px;"></i><br>
                    QR-Code hierher ziehen oder klicken zum Hochladen.<br>
                    Du kannst auch ein Bild aus der Zwischenablage einfügen (Ctrl+V / Cmd+V).
                </div>
                <input type="file" id="file-input" accept="image/*">
                
                <div class="alert error" id="error-message"></div>
                <div class="alert success" id="success-message"></div>
                
                <canvas id="qr-canvas"></canvas>

                <div class="result-container">
                     <label for="result">Dekodierter Inhalt:</label>
                     <textarea id="result" readonly placeholder="Der dekodierte Inhalt des QR-Codes wird hier angezeigt..."></textarea>
                </div>
                
                <div class="button-group">
                    <button id="copy-btn" class="copy-button">
                        <i class="icon fa-solid fa-copy"></i><span>Kopieren</span>
                    </button>
                    <button id="clear-btn" class="secondary">
                        <i class="icon fa-solid fa-trash"></i>Löschen
                    </button>
                </div>
            </div>
        </main>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');
        const qrCanvas = document.getElementById('qr-canvas');
        const ctx = qrCanvas.getContext('2d', { willReadFrequently: true });
        const resultContainer = document.getElementById('result');
        const errorMessage = document.getElementById('error-message');
        const successMessage = document.getElementById('success-message');
        const copyBtn = document.getElementById('copy-btn');
        const clearBtn = document.getElementById('clear-btn');

        dropZone.addEventListener('click', () => fileInput.click());

        fileInput.addEventListener('change', (e) => {
            const files = e.target.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
            // Reset file input to allow uploading the same file again
            fileInput.value = '';
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        document.addEventListener('paste', (e) => {
            const items = (e.clipboardData || window.clipboardData).items;
            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const blob = items[i].getAsFile();
                    if (blob) {
                         handleFile(blob);
                         // Prevent pasting text into input fields etc.
                         e.preventDefault(); 
                         break; 
                    }
                }
            }
        });

        function handleFile(file) {
            clearResultAndError();
            if (!file.type.startsWith('image/')) {
                displayError('Ungültiger Dateityp. Bitte lade ein Bild hoch.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(event) {
                const img = new Image();
                img.onload = function() {
                    drawImageAndDecode(img);
                }
                img.onerror = function() {
                    displayError('Fehler beim Laden des Bildes.');
                }
                img.src = event.target.result;
            }
            reader.onerror = function() {
                 displayError('Fehler beim Lesen der Datei.');
            }
            reader.readAsDataURL(file);
        }

        function drawImageAndDecode(img) {
             // Set canvas size to image size for better decoding accuracy
             // Limit canvas size to prevent performance issues with huge images
             const maxWidth = 1000;
             const maxHeight = 1000;
             let width = img.width;
             let height = img.height;

             if (width > maxWidth) {
                 height = (maxWidth / width) * height;
                 width = maxWidth;
             }
             if (height > maxHeight) {
                 width = (maxHeight / height) * width;
                 height = maxHeight;
             }
             
             qrCanvas.width = width;
             qrCanvas.height = height;
             
             try {
                // Draw the image onto the canvas
                ctx.drawImage(img, 0, 0, width, height);
                // Get the image data from the canvas
                const imageData = ctx.getImageData(0, 0, width, height);
                // Try to decode the QR code
                decodeQRCode(imageData);
             } catch (e) {
                 console.error("Canvas/Image Error:", e);
                 displayError('Fehler bei der Bildverarbeitung. Ist es ein gültiges Bild?');
             }
        }


        function decodeQRCode(imageData) {
            try {
                const code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });

                if (code) {
                    displayResult(code.data);
                    displaySuccess('QR-Code erfolgreich dekodiert!');
                } else {
                    displayError('Kein QR-Code im Bild gefunden.');
                }
            } catch (error) {
                 console.error("jsQR Error:", error);
                 displayError('Fehler beim Dekodieren des QR-Codes.');
            }
        }

        function displayResult(data) {
            resultContainer.value = data;
        }

        function displayError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }
        
        function displaySuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        function clearResultAndError() {
            resultContainer.value = '';
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }
        
        function isValidHttpUrl(string) {
          let url;
          try {
            url = new URL(string);
          } catch (_) {
            return false;  
          }
          return url.protocol === "http:" || url.protocol === "https:";
        }

        // Button functionality
        clearBtn.addEventListener('click', () => {
            clearResultAndError();
        });

        // Copy functionality - Simplified
        copyBtn.addEventListener('click', async () => {
            if (!resultContainer.value) {
                displayError('Nichts zum Kopieren!');
                return;
            }

            const originalHTML = copyBtn.innerHTML; // Store original HTML including icon and text
            let success = false;

            try {
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(resultContainer.value);
                    success = true;
                } else {
                    // Fallback for older browsers
                    resultContainer.select();
                    success = document.execCommand('copy');
                    if (!success) {
                         throw new Error('Fallback copy command failed');
                    }
                }
            } catch (err) {
                 console.error("Copy failed:", err);
                 displayError('Kopieren fehlgeschlagen!');
                 success = false;
            }

            if (success) {
                // Temporarily change button state on success
                copyBtn.innerHTML = `<i class="icon fa-solid fa-check"></i>Kopiert`;
                copyBtn.style.backgroundColor = 'var(--success-color)';
                copyBtn.disabled = true; // Disable button temporarily

                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML; // Restore original icon and text
                    copyBtn.style.backgroundColor = 'var(--primary-color)';
                    copyBtn.disabled = false; // Re-enable button
                }, 2000); // Revert after 2 seconds
            }
        });
    </script>
</body>
</html> 
