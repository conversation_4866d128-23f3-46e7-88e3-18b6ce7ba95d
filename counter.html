<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Counter</title>
    <meta property="og:title" content="Counter">
    <meta property="og:description" content="Ein einfacher Zähler zum Hoch- und Runterzählen.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/counter">
    <meta property="og:image" content="/assets/og-image.png">
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css">
    <style>
        :root {
            --text-color: #333333;
            --background-color: #FFFFFF;
            --primary-color: #007AFF;
            --secondary-color: #F5F5F7;
            --border-color: #E5E5E5;
            --button-bg: #EFEFF4;
            --button-hover-bg: #E5E5EA;
            --decrement-color: #FF3B30;
            --increment-color: #34C759;
            --decrement-hover-color: #FF453A;
            --increment-hover-color: #30D158;
            --border-radius: 8px;
            --transition: background-color 0.2s ease-in-out, transform 0.1s ease-in-out;
            --modal-overlay-bg: rgba(0, 0, 0, 0.4);
            --modal-bg: var(--secondary-color);
            --danger-color: #FF3B30;
            --danger-hover-color: #FF453A;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #F5F5F7;
                --background-color: #1E1E1E;
                --primary-color: #0A84FF;
                --secondary-color: #2C2C2E;
                --border-color: #3A3A3C;
                --button-bg: #3A3A3C;
                --button-hover-bg: #48484A;
                --decrement-color: #FF453A;
                --increment-color: #30D158;
                --decrement-hover-color: #FF594F;
                --increment-hover-color: #36E563;
            }
        }

        body {
            color: var(--text-color);
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            box-sizing: border-box;
        }

        main {
            max-width: 1200px;
            width: 100%;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        h1 {
            font-size: 24px;
            margin-bottom: 40px;
        }

        .counter-display {
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 20px;
            min-width: 100px;
            display: block;
            transition: transform 0.2s ease-out, color 0.2s ease-out;
        }

        .counter-display.updated {
            transform: scale(1.1);
        }

        .controls {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        button {
            color: #FFFFFF;
            border: none;
            border-radius: var(--border-radius);
            font-size: 24px;
            width: 60px;
            height: 60px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #decrementBtn {
            background-color: var(--decrement-color);
        }

        #decrementBtn:hover {
            background-color: var(--decrement-hover-color);
        }

        #incrementBtn {
            background-color: var(--increment-color);
        }
        
        #incrementBtn:hover {
            background-color: var(--increment-hover-color);
        }

        #resetBtn {
            background-color: var(--button-bg);
            color: var(--text-color);
        }

        #resetBtn:hover {
            background-color: var(--button-hover-bg);
        }

        .back-button {
            margin-top: auto;
            padding-top: 24px;
            text-align: center;
            width: 100%;
            max-width: 375px;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }
        
        @media (prefers-color-scheme: dark) {
           .back-button a:hover {
                background-color: rgba(10, 132, 255, 0.15);
            }
        }

        button:active {
            transform: scale(0.95);
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--modal-overlay-bg);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal {
            background-color: var(--modal-bg);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 300px;
        }

        .modal p {
            margin-bottom: 20px;
            font-size: 16px;
        }

        .modal-buttons {
            display: flex;
            justify-content: space-around;
            gap: 15px;
        }

        .modal button {
            flex-grow: 1;
            padding: 10px 0;
            font-size: 16px;
            height: auto;
            width: auto;
        }

        #confirmResetBtn {
            background-color: var(--danger-color);
            color: #FFFFFF;
        }

        #confirmResetBtn:hover {
            background-color: var(--danger-hover-color);
        }

        #cancelResetBtn {
             background-color: var(--button-bg);
             color: var(--text-color);
        }
        
        #cancelResetBtn:hover {
             background-color: var(--button-hover-bg);
        }
        
        .notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--secondary-color);
            color: var(--text-color);
            padding: 10px 20px;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            font-size: 14px;
        }

        .notification.show {
            opacity: 1;
        }
        
        #updateNotification {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--primary-color);
            color: #FFFFFF;
        }

        #updateNotification button {
            background-color: rgba(255, 255, 255, 0.2);
            color: #FFFFFF;
            border: none;
            padding: 5px 10px;
            margin-left: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            width: auto;
            height: auto;
        }
         #updateNotification button:hover {
             background-color: rgba(255, 255, 255, 0.3);
        }

        #offlineNotification .fa-unlink {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <main>
        
        <span id="countDisplay" class="counter-display">0</span>
        <div class="controls">
             <button id="decrementBtn" aria-label="Decrement"><i class="fa-solid fa-minus"></i></button>
             <button id="resetBtn" aria-label="Reset"><i class="fa-solid fa-rotate-left"></i></button> 
             <button id="incrementBtn" aria-label="Increment"><i class="fa-solid fa-plus"></i></button>
        </div>
    </main>
    <div class="back-button">
        <a href="index.html">← Zurück zur Übersicht</a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            
            const countDisplay = document.getElementById('countDisplay');
            const incrementBtn = document.getElementById('incrementBtn');
            const decrementBtn = document.getElementById('decrementBtn');
            const resetBtn = document.getElementById('resetBtn'); 
            const resetModalOverlay = document.getElementById('resetModalOverlay');
            const confirmResetBtn = document.getElementById('confirmResetBtn');
            const cancelResetBtn = document.getElementById('cancelResetBtn');

            
            let count = parseInt(localStorage.getItem('counterValue') || '0');

            function updateDisplay() {
                countDisplay.textContent = count;
            }

            function saveCount() {
                localStorage.setItem('counterValue', count.toString());
            }

            function triggerUpdateAnimation() {
                countDisplay.classList.add('updated');
                setTimeout(() => {
                    countDisplay.classList.remove('updated');
                }, 200); 
            }

            incrementBtn.addEventListener('click', () => {
                count++;
                updateDisplay();
                saveCount(); 
                triggerUpdateAnimation();
            });

            decrementBtn.addEventListener('click', () => {
                count--;
                updateDisplay();
                saveCount(); 
                triggerUpdateAnimation();
            });

            
            resetBtn.addEventListener('click', () => {
                resetModalOverlay.style.display = 'flex'; 
            });

            cancelResetBtn.addEventListener('click', () => {
                resetModalOverlay.style.display = 'none'; 
            });

            confirmResetBtn.addEventListener('click', () => {
                count = 0; 
                updateDisplay();
                saveCount();
                resetModalOverlay.style.display = 'none'; 
                triggerUpdateAnimation(); 
            });

            
            resetModalOverlay.addEventListener('click', (event) => {
                if (event.target === resetModalOverlay) {
                    resetModalOverlay.style.display = 'none';
                }
            });

            
            updateDisplay();
        });
    </script>

    <div id="resetModalOverlay" class="modal-overlay">
        <div class="modal">
            <p>Möchtest du den Zähler wirklich zurücksetzen?</p>
            <div class="modal-buttons">
                <button id="cancelResetBtn">Abbrechen</button>
                <button id="confirmResetBtn">Zurücksetzen</button>
            </div>
        </div>
    </div>
</body>
</html>
