<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Image Cropper">
    <meta property="og:description" content="Einfaches Tool zum Zuschneiden von Bildern im Browser.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/image-cropper">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Bild Cropper</title>
    <style>
        :root {
            --text-color: #333333;
            --background-color: #FFFFFF;
            --primary-color: #007AFF;
            --secondary-color: #F5F5F7;
            --border-color: #E5E5E5;
            --placeholder-color: #AAAAAA;
            --button-text-color: #FFFFFF;
            --button-bg-color: #007AFF;
            --button-hover-bg-color: #0056b3;
            --danger-color: #FF3B30;
            --danger-hover-color: #C70039;
            --dropzone-active-bg: rgba(0, 122, 255, 0.1);
            --border-radius: 8px;
            --transition: background-color 0.2s ease;
            --max-width: 1200px;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #F5F5F7;
                --background-color: #1E1E1E;
                --primary-color: #0A84FF;
                --secondary-color: #2C2C2E;
                --border-color: #3A3A3C;
                --placeholder-color: #888888;
                --button-text-color: #FFFFFF;
                --button-bg-color: #0A84FF;
                --button-hover-bg-color: #005ecb;
                --danger-color: #FF453A;
                --danger-hover-color: #d9362f;
                --dropzone-active-bg: rgba(10, 132, 255, 0.15);
            }
        }

        body {
            color: var(--text-color);
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            width: 100%;
            max-width: var(--max-width);
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 20px;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            background-color: var(--secondary-color);
            padding: 15px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .button {
            padding: 10px 18px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .button-primary {
            background-color: var(--button-bg-color);
            color: var(--button-text-color);
        }

        .button-primary:hover {
            background-color: var(--button-hover-bg-color);
        }
        
        .button-danger {
            background-color: var(--danger-color);
            color: var(--button-text-color);
        }
        
        .button-danger:hover {
            background-color: var(--danger-hover-color);
        }

        #download-button,
        #clear-button {
            margin-left: 15px;
        }

        input[type="file"] {
            display: none;
        }

        label.button {
            display: inline-flex; /* Ensure label behaves like a button */
        }

        .slider-container {
            display: flex;
            align-items: center;
        }
        
        #crop-size-value {
            margin-left: 5px;
            margin-right: 15px;
            min-width: 50px;
            text-align: right;
        }

        .slider-container label {
            white-space: nowrap;
        }

        input[type="range"] {
            cursor: pointer;
            accent-color: var(--primary-color);
        }

        .image-area {
            margin-top: 20px;
            position: relative;
            display: flex; /* Use flex to center placeholder */
            justify-content: center; /* Center placeholder horizontally */
            align-items: center; /* Center placeholder vertically */
            min-height: 300px; /* Give area a minimum height */
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden; /* Keep image within bounds */
            background-color: var(--secondary-color);
            transition: background-color 0.2s ease, border-color 0.2s ease; /* Added transition */
        }

        .image-area.dragover {
            background-color: var(--dropzone-active-bg);
            border-color: var(--primary-color);
        }

        .placeholder {
            color: var(--placeholder-color);
            text-align: center;
            padding: 20px;
            display: flex; /* Use flex for placeholder content */
            flex-direction: column; /* Stack text and button */
            align-items: center; /* Center items */
            justify-content: center; /* Center items vertically */
            gap: 15px; /* Space between text and button */
        }
        
        .image-container {
             position: relative;
             cursor: grab;
             display: inline-block; /* Fit container to image size */
             line-height: 0; /* Remove extra space below image */
        }

        #uploaded-image {
            display: block;
            max-width: 100%;
            max-height: 70vh;
            user-select: none;
        }

        .crop-overlay {
            position: absolute;
            box-sizing: border-box;
            border: 2px solid rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
            cursor: move;
        }

        .back-button {
            margin-top: 30px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1); /* Adjust alpha for dark mode */
        }
        
        @media (prefers-color-scheme: dark) {
            .back-button a:hover {
                 background-color: rgba(10, 132, 255, 0.15);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Image Cropper</h1>
        </header>

        <main>
            <div class="controls" style="display: none;">
                <div id="crop-controls" style="display: none;">
                     <div class="slider-container">
                        <label for="crop-size" style="display: none;">Größe:</label>
                        <input type="range" id="crop-size" min="50" max="500" value="200">
                        <span id="crop-size-value">200px</span>
                    </div>
                    <button id="download-button" class="button button-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                           <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                           <polyline points="7 10 12 15 17 10"/>
                           <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        <span>Ausschnitt herunterladen</span>
                    </button>
                     <button id="clear-button" class="button button-danger">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                           <polyline points="3 6 5 6 21 6"></polyline>
                           <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                           <line x1="10" y1="11" x2="10" y2="17"></line>
                           <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                        <span>Bild entfernen</span>
                     </button>
                </div>
            </div>
            
            <input type="file" id="file-input" accept="image/*" style="display: none;">

            <div class="image-area" id="image-area">
                <div class="placeholder" id="placeholder">
                    <div>Lade ein Bild hoch oder füge es mit Strg+V / Cmd+V ein</div>
                     <label for="file-input" class="button button-primary">
                         <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="17 8 12 3 7 8"/>
                            <line x1="12" y1="3" x2="12" y2="15"/>
                        </svg>
                        <span>Bild auswählen</span>
                    </label>
                </div>
                <div class="image-container" id="image-container" style="display: none;">
                    <img id="uploaded-image" src="#" alt="Hochgeladenes Bild">
                    <div class="crop-overlay" id="crop-overlay"></div>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="back-button">
                <a href="index.html">← Zurück zur Übersicht</a>
            </div>
        </footer>
    </div>

    <script>
        const imageArea = document.getElementById('image-area');
        const placeholder = document.getElementById('placeholder');
        const imageContainer = document.getElementById('image-container');
        const uploadedImage = document.getElementById('uploaded-image');
        const cropOverlay = document.getElementById('crop-overlay');
        const fileInput = document.getElementById('file-input');
        const cropSizeSlider = document.getElementById('crop-size');
        const cropSizeValue = document.getElementById('crop-size-value');
        const downloadButton = document.getElementById('download-button');
        const clearButton = document.getElementById('clear-button');
        const cropControls = document.getElementById('crop-controls');
        const controlsContainer = document.querySelector('.controls'); // Get the main controls container

        let imageSrc = null;
        let cropPosition = { x: 0, y: 0 };
        let cropSize = parseInt(cropSizeSlider.value);
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let imageNaturalSize = { width: 0, height: 0 };
        let imageDisplaySize = { width: 0, height: 0 };

        function displayImage(src) {
            imageSrc = src;
            uploadedImage.src = src;
            imageContainer.style.display = 'inline-block';
            placeholder.style.display = 'none';
            controlsContainer.style.display = 'flex'; // Show the main controls container
            cropControls.style.display = 'flex'; // Show crop controls group
            imageArea.style.border = 'none';
             imageArea.style.backgroundColor = 'transparent';

            uploadedImage.onload = () => {
                imageNaturalSize = { width: uploadedImage.naturalWidth, height: uploadedImage.naturalHeight };
                imageDisplaySize = { width: uploadedImage.offsetWidth, height: uploadedImage.offsetHeight };
                
                // Reset and center crop overlay
                const maxPossibleSize = Math.min(imageDisplaySize.width, imageDisplaySize.height);
                cropSizeSlider.max = Math.max(50, maxPossibleSize); // Ensure max is at least 50
                
                // Adjust initial crop size if it's larger than the image
                cropSize = Math.min(parseInt(cropSizeSlider.value), maxPossibleSize);
                cropSize = Math.max(50, cropSize); // Ensure min size is 50
                cropSizeSlider.value = cropSize;
                cropSizeValue.textContent = `${cropSize}px`;

                cropPosition.x = (imageDisplaySize.width - cropSize) / 2;
                cropPosition.y = (imageDisplaySize.height - cropSize) / 2;
                
                updateCropOverlay();
            };
        }
        
        function clearImage() {
            imageSrc = null;
            uploadedImage.src = '#';
            imageContainer.style.display = 'none';
            placeholder.style.display = 'flex'; // Use flex to show placeholder correctly
            cropControls.style.display = 'none'; // Hide crop controls group
            controlsContainer.style.display = 'none'; // Hide the main controls container
            fileInput.value = null; // Reset file input
            imageArea.style.border = '2px dashed var(--border-color)';
             imageArea.style.backgroundColor = 'var(--secondary-color)';
        }

        function updateCropOverlay() {
            cropOverlay.style.width = `${cropSize}px`;
            cropOverlay.style.height = `${cropSize}px`;
            
            // Ensure crop overlay stays within image bounds
            cropPosition.x = Math.max(0, Math.min(cropPosition.x, imageDisplaySize.width - cropSize));
            cropPosition.y = Math.max(0, Math.min(cropPosition.y, imageDisplaySize.height - cropSize));
            
            cropOverlay.style.left = `${cropPosition.x}px`;
            cropOverlay.style.top = `${cropPosition.y}px`;
        }

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => displayImage(event.target.result);
                reader.readAsDataURL(file);
            }
        });

        document.addEventListener('paste', (e) => {
            const items = e.clipboardData?.items;
            if (!items) return;

            for (let item of items) {
                if (item.type.includes('image')) {
                    const file = item.getAsFile();
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => displayImage(event.target.result);
                        reader.readAsDataURL(file);
                        break; 
                    }
                }
            }
        });

        cropOverlay.addEventListener('mousedown', (e) => {
            if (!imageSrc) return;
            isDragging = true;
            dragStart = {
                x: e.clientX - cropPosition.x,
                y: e.clientY - cropPosition.y
            };
            cropOverlay.style.cursor = 'grabbing';
            imageContainer.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging || !imageSrc) return;
            
            let newX = e.clientX - dragStart.x;
            let newY = e.clientY - dragStart.y;

            // Constrain movement within the image boundaries
            newX = Math.max(0, Math.min(newX, imageDisplaySize.width - cropSize));
            newY = Math.max(0, Math.min(newY, imageDisplaySize.height - cropSize));

            cropPosition.x = newX;
            cropPosition.y = newY;
            
            updateCropOverlay();
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                cropOverlay.style.cursor = 'move';
                 imageContainer.style.cursor = 'grab';
            }
        });

        cropSizeSlider.addEventListener('input', (e) => {
            const newSize = parseInt(e.target.value);
            const maxPossibleSize = Math.min(imageDisplaySize.width, imageDisplaySize.height);
            cropSize = Math.min(newSize, maxPossibleSize);
            cropSize = Math.max(50, cropSize); // Enforce minimum size

            // Adjust position if the new size pushes the overlay out of bounds
            cropPosition.x = Math.min(cropPosition.x, imageDisplaySize.width - cropSize);
            cropPosition.y = Math.min(cropPosition.y, imageDisplaySize.height - cropSize);
            
            cropSizeValue.textContent = `${cropSize}px`;
            updateCropOverlay();
        });
        
        clearButton.addEventListener('click', clearImage);

        downloadButton.addEventListener('click', () => {
            if (!imageSrc) return;

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const scale = imageNaturalSize.width / imageDisplaySize.width;

            canvas.width = cropSize;
            canvas.height = cropSize;

            ctx.drawImage(
                uploadedImage,
                cropPosition.x * scale, // Source x
                cropPosition.y * scale, // Source y
                cropSize * scale,       // Source width
                cropSize * scale,       // Source height
                0,                      // Destination x
                0,                      // Destination y
                cropSize,               // Destination width
                cropSize                // Destination height
            );

            const link = document.createElement('a');
            link.download = 'cropped-image.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });
        
        // Adjust crop size limits and position on window resize
        window.addEventListener('resize', () => {
            if (!imageSrc) return;
            
            // Allow image dimensions to recalculate
            requestAnimationFrame(() => {
                 const oldDisplayWidth = imageDisplaySize.width;
                 imageDisplaySize = { width: uploadedImage.offsetWidth, height: uploadedImage.offsetHeight };
                 const scaleRatio = imageDisplaySize.width / oldDisplayWidth;

                 const maxPossibleSize = Math.min(imageDisplaySize.width, imageDisplaySize.height);
                 cropSizeSlider.max = Math.max(50, maxPossibleSize);

                 // Scale existing crop size and position
                 cropSize = Math.round(cropSize * scaleRatio);
                 cropPosition.x = Math.round(cropPosition.x * scaleRatio);
                 cropPosition.y = Math.round(cropPosition.y * scaleRatio);

                 // Clamp size and update slider
                 cropSize = Math.max(50, Math.min(cropSize, maxPossibleSize));
                 cropSizeSlider.value = cropSize;
                 cropSizeValue.textContent = `${cropSize}px`;
                
                 updateCropOverlay(); // This will also clamp position
            });
        });

        // --- Drag and Drop ---
        imageArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!imageArea.classList.contains('dragover')) {
                 imageArea.classList.add('dragover');
            }
        });

        imageArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
             imageArea.classList.remove('dragover');
        });

        imageArea.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
             imageArea.classList.remove('dragover');
            
            const files = e.dataTransfer?.files;
            if (files && files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (event) => displayImage(event.target.result);
                    reader.readAsDataURL(file);
                } else {
                    // Optional: Handle non-image files
                    console.warn("Nur Bilddateien können abgelegt werden.");
                    // alert("Nur Bilddateien können abgelegt werden."); 
                }
            }
        });
        // --- End Drag and Drop ---

    </script>
</body>
</html>
