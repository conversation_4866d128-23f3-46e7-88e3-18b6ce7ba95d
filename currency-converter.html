<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Währungsumrechner">
    <meta property="og:description" content="Rechne Währungen einfach und schnell um mit aktuellen Wechselkursen.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/currency-converter">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Währungsumrechner</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css" 
          integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a" 
          crossorigin="anonymous">
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 16px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --card-padding: 24px;
            --transition: all 0.3s ease;
            --accent-color: #5E5CE6;
            --success-color: #34C759;
            --error-color: #FF3B30;
            --card-hover-transform: translateY(-5px);
            --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            --info-value-bg: rgba(0, 0, 0, 0.02);
            --border-color: rgba(0, 0, 0, 0.05);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                --accent-color: #5E5CE6;
                --success-color: #30D158;
                --error-color: #FF453A;
                --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
                --info-value-bg: rgba(255, 255, 255, 0.05);
                --border-color: rgba(255, 255, 255, 0.1);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 16px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 4px;
        }

        header {
            margin-bottom: 40px;
            text-align: center;
            padding: 0 8px;
        }

        h1 {
            font-size: clamp(1.75rem, 5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-color);
            word-wrap: break-word;
        }

        .subtitle {
            font-size: clamp(1rem, 3vw, 1.2rem);
            color: var(--secondary-text-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .converter-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: var(--card-padding);
            margin-bottom: 24px;
        }

        .conversion-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }

        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background-color: var(--info-value-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: var(--transition);
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        button:hover {
            opacity: 0.9;
        }

        button.swap-button {
            background-color: var(--accent-color);
            flex: 0 0 auto;
        }

        .result-container {
            margin-top: 30px;
            display: none;
        }

        .result-card {
            background-color: var(--info-value-bg);
            border-radius: var(--border-radius);
            padding: 20px;
            text-align: center;
        }

        .result-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .result-details {
            font-size: 1.1rem;
            color: var(--secondary-text-color);
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading i {
            color: var(--primary-color);
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            display: none;
            color: var(--error-color);
            background-color: rgba(255, 59, 48, 0.1);
            padding: 12px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .home-link {
            display: inline-block;
            margin-top: 30px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .home-link:hover {
            opacity: 0.8;
        }

        .home-link i {
            margin-right: 6px;
        }

        .additional-info {
            margin-top: 40px;
            padding: 20px;
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .additional-info h3 {
            color: var(--text-color);
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .additional-info p {
            color: var(--secondary-text-color);
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .data-source {
            font-style: italic;
            font-size: 0.9rem;
            color: var(--secondary-text-color);
            margin-top: 10px;
        }

        .data-source a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
            max-width: 375px;
            margin-left: auto;
            margin-right: auto;
            display: flex;
            justify-content: center;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px 0;
            color: var(--secondary-text-color);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fa-solid fa-money-bill-transfer"></i> Währungsumrechner</h1>
            <p class="subtitle">Rechne Beträge zwischen verschiedenen Währungen um mit aktuellen Wechselkursen</p>
        </header>

        <div class="converter-card">
            
            <form id="currency-form" class="conversion-form">
                <div class="form-group">
                    <label for="amount">Betrag</label>
                    <input type="number" id="amount" placeholder="0.00" min="0" step="0.01" value="1">
                </div>
                
                <div class="form-group">
                    <label for="from-currency">Von Währung</label>
                    <select id="from-currency"></select>
                </div>
                
                <div class="form-group">
                    <label for="to-currency">Zu Währung</label>
                    <select id="to-currency"></select>
                </div>
                
                <div class="action-buttons">
                    <button type="submit" id="convert-btn" class="primary-btn">Umrechnen</button>
                    <button type="button" id="swap-btn" class="swap-button"><i class="fa-solid fa-right-left"></i></button>
                </div>
            </form>
            
            <div class="loading">
                <i class="fa-solid fa-spinner"></i>
            </div>
            
            <div id="error-message" class="error-message"></div>
            
            <div id="result-container" class="result-container">
                <div class="result-card">
                    <div id="result-value" class="result-value"></div>
                    <div id="result-details" class="result-details"></div>
                </div>
            </div>
        </div>
        
        <div class="additional-info">
            <h3>Über diesen Währungsrechner</h3>
            <p>Dieser Währungsumrechner verwendet die aktuellen Wechselkurse für über 30 Währungen. Die Werte werden in Echtzeit bezogen und sind stets aktuell.</p>
            <p class="data-source">Datenquelle: <a href="https://www.frankfurter.app/" target="_blank">Frankfurter API</a></p>
        </div>
        
        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const amountInput = document.getElementById('amount');
            const fromCurrencySelect = document.getElementById('from-currency');
            const toCurrencySelect = document.getElementById('to-currency');
            const convertBtn = document.getElementById('convert-btn');
            const swapBtn = document.getElementById('swap-btn');
            const resultContainer = document.getElementById('result-container');
            const resultValue = document.getElementById('result-value');
            const resultDetails = document.getElementById('result-details');
            const loadingIndicator = document.querySelector('.loading');
            const errorMessage = document.getElementById('error-message');
            const currencyForm = document.getElementById('currency-form');
            
            // Lade verfügbare Währungen
            async function loadCurrencies() {
                try {
                    loadingIndicator.style.display = 'block';
                    const response = await fetch('https://api.frankfurter.app/currencies');
                    
                    if (!response.ok) {
                        throw new Error('Fehler beim Laden der Währungen');
                    }
                    
                    const currencies = await response.json();
                    
                    // Füge Währungen zu den Dropdown-Menüs hinzu
                    Object.entries(currencies).forEach(([code, name]) => {
                        const fromOption = document.createElement('option');
                        fromOption.value = code;
                        fromOption.textContent = `${code} - ${name}`;
                        fromCurrencySelect.appendChild(fromOption);
                        
                        const toOption = document.createElement('option');
                        toOption.value = code;
                        toOption.textContent = `${code} - ${name}`;
                        toCurrencySelect.appendChild(toOption);
                    });
                    
                    // Standardwerte setzen
                    fromCurrencySelect.value = 'USD';
                    toCurrencySelect.value = 'EUR';
                    
                } catch (error) {
                    showError(error.message);
                } finally {
                    loadingIndicator.style.display = 'none';
                }
            }
            
            // Führe die Währungsumrechnung durch
            async function convertCurrency() {
                const amount = parseFloat(amountInput.value);
                const fromCurrency = fromCurrencySelect.value;
                const toCurrency = toCurrencySelect.value;
                
                // Validiere Eingabe
                if (isNaN(amount) || amount <= 0) {
                    showError('Bitte gib einen gültigen Betrag ein');
                    return;
                }
                
                if (fromCurrency === toCurrency) {
                    resultValue.textContent = new Intl.NumberFormat(undefined, {
                        style: 'currency',
                        currency: toCurrency
                    }).format(amount);
                    resultDetails.textContent = `1 ${fromCurrency} = 1 ${toCurrency}`;
                    resultContainer.style.display = 'block';
                    return;
                }
                
                try {
                    // Verstecke vorherige Ergebnisse und Fehler
                    resultContainer.style.display = 'none';
                    errorMessage.style.display = 'none';
                    loadingIndicator.style.display = 'block';
                    
                    // API-Anfrage
                    const response = await fetch(`https://api.frankfurter.app/latest?amount=${amount}&from=${fromCurrency}&to=${toCurrency}`);
                    
                    if (!response.ok) {
                        throw new Error('Fehler bei der Umrechnung');
                    }
                    
                    const data = await response.json();
                    
                    // Zeige das Ergebnis an
                    if (data && data.rates && data.rates[toCurrency]) {
                        const convertedAmount = data.rates[toCurrency];
                        const rate = convertedAmount / amount;
                        
                        resultValue.textContent = new Intl.NumberFormat(undefined, {
                            style: 'currency',
                            currency: toCurrency
                        }).format(convertedAmount);
                        
                        resultDetails.textContent = `1 ${fromCurrency} = ${rate.toFixed(6)} ${toCurrency}`;
                        resultContainer.style.display = 'block';
                    } else {
                        throw new Error('Ungültige Antwort vom Server');
                    }
                    
                } catch (error) {
                    showError(error.message);
                } finally {
                    loadingIndicator.style.display = 'none';
                }
            }
            
            // Zeige Fehlermeldung an
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                resultContainer.style.display = 'none';
            }
            
            // Event Listener für Formular-Submit (statt Button-Click)
            currencyForm.addEventListener('submit', (e) => {
                e.preventDefault(); // Verhindere Standard-Formular-Submit
                convertCurrency();
            });
            
            fromCurrencySelect.addEventListener('change', convertCurrency);
            toCurrencySelect.addEventListener('change', convertCurrency);
            
            // Swap-Währungen und führe Umrechnung aus
            swapBtn.addEventListener('click', async () => {
                const tempCurrency = fromCurrencySelect.value;
                fromCurrencySelect.value = toCurrencySelect.value;
                toCurrencySelect.value = tempCurrency;
                await convertCurrency();
            });
            
            // Starte mit dem Laden der Währungen
            await loadCurrencies();
            
            // Führe erste Umrechnung durch
            await convertCurrency();
        });
    </script>
</body>
</html>
