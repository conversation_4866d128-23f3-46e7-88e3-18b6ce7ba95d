<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta property="og:title" content="YouTube Thumbnail Extractor">
  <meta property="og:description" content="Extrahiert Thumbnails verschiedener Qualitätsstufen aus YouTube-Video-URLs.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://tools.nyanya.de/yt-thumbs">
  <meta property="og:image" content="/assets/og-image.png">
  <title>YouTube Thumbnails</title>
  <style>
    :root {
      --text-color: #333333;
      --background-color: #f5f5f7; /* Slightly off-white background */
      --primary-color: #FF0000; /* YouTube Red */
      --secondary-color: #FFFFFF; /* Card background */
      --border-color: #d1d1d6;
      --button-hover-color: #cc0000;
      --link-hover-color: #cc0000;
      --error-color: #FF0000;
      --border-radius: 8px;
      --transition: background-color 0.2s ease;
      --input-border-color: #c7c7cc;
    }

    @media (prefers-color-scheme: dark) {
      :root {
        --text-color: #F5F5F7;
        --background-color: #1E1E1E;
        --primary-color: #FF453A; /* Lighter YouTube Red for dark mode */
        --secondary-color: #2C2C2E; /* Darker card background */
        --border-color: #3A3A3C;
        --button-hover-color: #E03D33;
        --link-hover-color: #FF453A;
        --error-color: #FF453A;
        --input-border-color: #3A3A3C;
      }
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: var(--background-color);
      color: var(--text-color);
      line-height: 1.5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
    }
    h1 {
      font-size: 2em;
      text-align: center;
      margin-bottom: 1em;
      color: var(--primary-color);
    }
    form {
      display: flex;
      flex-direction: column;
      margin-bottom: 2em;
    }
    input[type="text"] {
      padding: 10px;
      font-size: 1em;
      margin-bottom: 1em;
      border: 1px solid var(--input-border-color);
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: var(--text-color);
    }
    button {
      padding: 10px;
      font-size: 1em;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--primary-color);
      color: white; /* Button text usually stays white */
      cursor: pointer;
      transition: var(--transition);
    }
    button:hover {
      background-color: var(--button-hover-color);
    }
    .thumbnails {
      display: block; /* Changed from grid to block for simplicity */
    }
    .thumbnail {
      background-color: var(--secondary-color);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: 10px;
      text-align: center;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      margin-bottom: 1em;
    }
    .thumbnail img {
      max-width: 100%;
      border-radius: var(--border-radius);
      cursor: pointer;
    }
    .thumbnail p a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: bold;
    }
    .thumbnail p a:hover {
      color: var(--link-hover-color);
      text-decoration: underline;
    }
    .error {
      color: var(--error-color);
      text-align: center;
      margin-bottom: 1em;
    }
    .back-button {
        margin-top: 24px;
        text-align: center;
        width: 100%;
        /* max-width: 375px; Removed max-width as container already limits width */
    }

    .back-button a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .back-button a:hover {
        background-color: rgba(0, 122, 255, 0.1); /* Using a generic hover, consider adjusting */
         /* Let's use the primary color hover logic, maybe dimmed */
         background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>YouTube Thumbnails</h1>
    <form id="urlForm">
      <input autofocus type="text" id="ytUrl" placeholder="YouTube URL eingeben">
      <button type="submit">Thumbnails anzeigen</button>
    </form>
    <div id="error" class="error"></div>
    <div id="thumbnails" class="thumbnails"></div>
    <div class="back-button">
      <a href="index.html">← Zurück zur Übersicht</a>
    </div>
  </div>
  <script>
    document.getElementById('urlForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const url = document.getElementById('ytUrl').value.trim();
      const videoId = extractVideoId(url);
      const errorDiv = document.getElementById('error');
      const thumbnailsDiv = document.getElementById('thumbnails');
      
      errorDiv.textContent = '';
      thumbnailsDiv.innerHTML = '';
      
      if (!videoId) {
        errorDiv.textContent = 'Ungültige YouTube URL.';
        return;
      }
      
      // Thumbnail-Größen in umgekehrter Reihenfolge: größtes Thumbnail zuerst.
      const sizes = [
        { name: 'maxresdefault', label: 'Max Resolution' },
        { name: 'sddefault', label: 'Standard Definition' },
        { name: 'hqdefault', label: 'High Quality' },
        { name: 'mqdefault', label: 'Medium Quality' },
        { name: 'default', label: 'Default' }
      ];
      
      sizes.forEach(size => {
        const imgUrl = `https://img.youtube.com/vi/${videoId}/${size.name}.jpg`;
        const div = document.createElement('div');
        div.className = 'thumbnail';
        
        const img = document.createElement('img');
        img.src = imgUrl;
        img.alt = `${size.label} Thumbnail`;
        img.addEventListener('click', function() {
          window.open(imgUrl, '_blank');
        });
        
        const caption = document.createElement('p');
        const link = document.createElement('a');
        link.href = imgUrl;
        link.target = '_blank';
        link.textContent = size.label;
        caption.appendChild(link);
        
        div.appendChild(img);
        div.appendChild(caption);
        thumbnailsDiv.appendChild(div);
      });
    });
    
    function extractVideoId(url) {
      let regExp = /[?&]v=([^&#]+)/;
      let match = url.match(regExp);
      if (match && match[1]) return match[1];
      regExp = /youtu\.be\/([^?&#]+)/;
      match = url.match(regExp);
      if (match && match[1]) return match[1];
      regExp = /\/embed\/([^?&#]+)/;
      match = url.match(regExp);
      if (match && match[1]) return match[1];
      return null;
    }
  </script>
</body>
</html>
