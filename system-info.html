<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Systeminformationen">
    <meta property="og:description" content="Zeigt detaillierte Informationen zu deinem Betriebssystem und Browser an.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/system-info">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Systeminformationen</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css">
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 16px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --card-padding: 24px;
            --transition: all 0.3s ease;
            --accent-color: #5E5CE6;
            --success-color: #34C759;
            --card-hover-transform: translateY(-5px);
            --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            --info-value-bg: rgba(0, 0, 0, 0.02);
            --border-color: rgba(0, 0, 0, 0.05);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                --accent-color: #5E5CE6;
                --success-color: #30D158;
                --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
                --info-value-bg: rgba(255, 255, 255, 0.05);
                --border-color: rgba(255, 255, 255, 0.1);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
            background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.05), transparent);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            margin-bottom: 40px;
            text-align: center;
            padding: 20px 0;
        }

        h1 {
            font-size: 36px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        h1 i {
            margin-right: 12px;
            color: var(--text-color);
        }

        .subtitle {
            color: var(--secondary-text-color);
            font-size: 18px;
            font-weight: 400;
        }

        main {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: var(--card-padding);
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }

        .info-card:hover {
            transform: var(--card-hover-transform);
            box-shadow: var(--card-hover-shadow);
        }

        h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .icon-section {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }

        .info-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .info-label {
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .info-label i {
            width: 16px;
            text-align: center;
            color: var(--secondary-text-color);
        }

        .info-value {
            color: var(--secondary-text-color);
            text-align: right;
            font-size: 14px;
            background-color: var(--info-value-bg);
            padding: 4px 10px;
            border-radius: 6px;
            font-family: 'SF Mono', SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
            word-break: break-all;
            max-width: 60%;
            overflow-wrap: break-word;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }

        @media (max-width: 768px) {
            main {
                grid-template-columns: 1fr;
            }
            
            .info-card {
                padding: 20px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .subtitle {
                font-size: 16px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-value {
                text-align: left;
                margin-top: 4px;
                max-width: 100%;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-desktop"></i> Systeminformationen</h1>
        </header>
        
        <main>
            <section class="info-card" id="browser-info">
                <h2><i class="fas fa-globe icon-section"></i> Browser</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="display-info">
                <h2><i class="fas fa-mobile-alt icon-section"></i> Anzeige</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="system-info">
                <h2><i class="fas fa-cogs icon-section"></i> System</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="network-info">
                <h2><i class="fas fa-network-wired icon-section"></i> Netzwerk</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="memory-info">
                <h2><i class="fas fa-memory icon-section"></i> Speicher</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="battery-info">
                <h2><i class="fas fa-battery-half icon-section"></i> Batterie</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="features-info">
                <h2><i class="fas fa-star icon-section"></i> Funktionen</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="locale-info">
                <h2><i class="fas fa-globe-americas icon-section"></i> Lokalisierung</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="media-info">
                <h2><i class="fas fa-video icon-section"></i> Mediengeräte</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="security-info">
                <h2><i class="fas fa-lock icon-section"></i> Sicherheit</h2>
                <div class="info-content"></div>
            </section>
            
            <section class="info-card" id="performance-info">
                <h2><i class="fas fa-bolt icon-section"></i> Performance</h2>
                <div class="info-content"></div>
            </section>
        </main>
        
        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Funktionen zum Abrufen und Anzeigen der Systeminformationen
            getBrowserInfo();
            getDisplayInfo();
            getSystemInfo();
            getNetworkInfo();
            getMemoryInfo();
            getBatteryInfo();
            getFeaturesInfo();
            getLocaleInfo();
            getMediaInfo();
            getSecurityInfo();
            getPerformanceInfo();
            
            // Event-Listener für Änderungen der Systeminformationen
            window.addEventListener('resize', getDisplayInfo);
            window.addEventListener('online', getNetworkInfo);
            window.addEventListener('offline', getNetworkInfo);
            
            // Performance-Metriken nach dem vollständigen Laden der Seite
            window.addEventListener('load', function() {
                setTimeout(getPerformanceInfo, 100);
            });
        });

        // Hilfsfunktion zum Erstellen von Informations-Elementen
        function createInfoItem(label, value, iconClass = '') {
            // Wenn der Wert "Nicht verfügbar" ist, nicht anzeigen
            if (value === 'Nicht verfügbar') {
                return null;
            }
            
            const item = document.createElement('div');
            item.className = 'info-item';
            
            const labelElement = document.createElement('span');
            labelElement.className = 'info-label';
            
            if (iconClass) {
                const iconElement = document.createElement('i');
                iconElement.className = iconClass;
                labelElement.appendChild(iconElement);
            }
            
            const labelText = document.createTextNode(label);
            labelElement.appendChild(labelText);
            
            const valueElement = document.createElement('span');
            valueElement.className = 'info-value';
            valueElement.textContent = value;
            
            item.appendChild(labelElement);
            item.appendChild(valueElement);
            
            return item;
        }

        // Browser-Informationen
        function getBrowserInfo() {
            const container = document.querySelector('#browser-info .info-content');
            container.innerHTML = '';
            
            const userAgent = navigator.userAgent;
            const browserInfo = detectBrowser();
            const language = navigator.language;
            const cookiesEnabled = navigator.cookieEnabled ? 'Aktiviert' : 'Deaktiviert';
            const doNotTrack = navigator.doNotTrack ? 'Aktiviert' : 'Deaktiviert';
            
            appendInfoItem(container, createInfoItem('Browser', browserInfo, 'fas fa-search'));
            appendInfoItem(container, createInfoItem('User Agent', userAgent, 'fas fa-wrench'));
            appendInfoItem(container, createInfoItem('Sprache', language, 'fas fa-comment'));
            appendInfoItem(container, createInfoItem('Cookies', cookiesEnabled, 'fas fa-cookie'));
            appendInfoItem(container, createInfoItem('Do Not Track', doNotTrack, 'fas fa-shield-alt'));
        }

        // Funktion zum Erkennen des Browser-Typs und der Version
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browser = 'Unbekannt';
            let version = '';
            
            if (userAgent.match(/chrome|chromium|crios/i)) {
                browser = 'Chrome';
                const match = userAgent.match(/(?:chrome|chromium|crios)\/([\d.]+)/i);
                if (match) version = match[1];
            } else if (userAgent.match(/firefox|fxios/i)) {
                browser = 'Firefox';
                const match = userAgent.match(/(?:firefox|fxios)\/([\d.]+)/i);
                if (match) version = match[1];
            } else if (userAgent.match(/safari/i)) {
                browser = 'Safari';
                const match = userAgent.match(/version\/([\d.]+)/i);
                if (match) version = match[1];
            } else if (userAgent.match(/opr\//i)) {
                browser = 'Opera';
                const match = userAgent.match(/opr\/([\d.]+)/i);
                if (match) version = match[1];
            } else if (userAgent.match(/edg/i)) {
                browser = 'Edge';
                const match = userAgent.match(/edg(?:e|ios|a)\/([\d.]+)/i);
                if (match) version = match[1];
            }
            
            return version ? `${browser} ${version}` : browser;
        }

        // Anzeige-Informationen
        function getDisplayInfo() {
            const container = document.querySelector('#display-info .info-content');
            container.innerHTML = '';
            
            const screenWidth = window.screen.width;
            const screenHeight = window.screen.height;
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const pixelRatio = window.devicePixelRatio;
            const colorDepth = window.screen.colorDepth;
            const orientation = screen.orientation ? screen.orientation.type : 'Nicht verfügbar';
            
            appendInfoItem(container, createInfoItem('Bildschirmauflösung', `${screenWidth} × ${screenHeight}`, 'fas fa-ruler-combined'));
            appendInfoItem(container, createInfoItem('Fenster-Größe', `${windowWidth} × ${windowHeight}`, 'fas fa-window-maximize'));
            appendInfoItem(container, createInfoItem('Pixel-Verhältnis', pixelRatio.toFixed(2), 'fas fa-search-plus'));
            appendInfoItem(container, createInfoItem('Farbtiefe', `${colorDepth} bit`, 'fas fa-palette'));
            appendInfoItem(container, createInfoItem('Orientierung', orientation, 'fas fa-mobile'));
        }

        // System-Informationen
        function getSystemInfo() {
            const container = document.querySelector('#system-info .info-content');
            container.innerHTML = '';
            
            const platform = navigator.platform;
            const cpuCores = navigator.hardwareConcurrency || 'Nicht verfügbar';
            const osName = detectOS();
            const touchSupport = 'ontouchstart' in window ? 'Ja' : 'Nein';
            const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'Ja' : 'Nein';
            const colorScheme = prefersDarkMode ? 'Dunkel' : 'Hell';
            
            appendInfoItem(container, createInfoItem('Betriebssystem', osName, 'fas fa-laptop'));
            appendInfoItem(container, createInfoItem('Plattform', platform, 'fas fa-server'));
            appendInfoItem(container, createInfoItem('CPU-Kerne', cpuCores, 'fas fa-microchip'));
            appendInfoItem(container, createInfoItem('Touch-Unterstützung', touchSupport, 'fas fa-hand-pointer'));
            appendInfoItem(container, createInfoItem('Farbschema', colorScheme, 'fas fa-adjust'));
            
            // Event-Listener für Änderungen des Farbschemas
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', getSystemInfo);
        }

        // Funktion zum Erkennen des Betriebssystems
        function detectOS() {
            const userAgent = navigator.userAgent;
            
            if (/Windows/.test(userAgent)) {
                return 'Windows';
            } else if (/Macintosh|MacIntel|MacPPC|Mac68K/.test(userAgent)) {
                return 'macOS';
            } else if (/iPhone|iPad|iPod/.test(userAgent)) {
                return 'iOS';
            } else if (/Android/.test(userAgent)) {
                return 'Android';
            } else if (/Linux/.test(userAgent)) {
                return 'Linux';
            }
            
            return 'Unbekannt';
        }

        // Netzwerk-Informationen
        function getNetworkInfo() {
            const container = document.querySelector('#network-info .info-content');
            container.innerHTML = '';
            
            const online = navigator.onLine ? 'Ja' : 'Nein';
            appendInfoItem(container, createInfoItem('Online', online, 'fas fa-wifi'));
            
            // IP-Adresse des Besuchers abrufen
            fetch('/ip')
                .then(response => response.json())
                .then(data => {
                    // IP-Adresse anzeigen
                    appendInfoItem(container, createInfoItem('IP-Adresse', data.ip, 'fas fa-globe'));
                    
                    // IP-Typ anzeigen, wenn verfügbar
                    if (data.type) {
                        appendInfoItem(container, createInfoItem('IP-Typ', data.type, 'fas fa-network-wired'));
                    }
                })
                .catch(error => {
                    console.error('Fehler beim Abrufen der IP-Adresse:', error);
                    appendInfoItem(container, createInfoItem('IP-Adresse', 'Nicht verfügbar', 'fas fa-globe'));
                });
            
            // Nur RTT anzeigen, Downlink und Verbindungstyp entfernt
            if (navigator.connection && navigator.connection.rtt) {
                appendInfoItem(container, createInfoItem('RTT', `${navigator.connection.rtt} ms`, 'fas fa-stopwatch'));
            }
            
            // DNS-Präfetch-Status
            const dnsPrefetchControl = document.createElement('link').relList.supports('dns-prefetch') ? 'Unterstützt' : 'Nicht unterstützt';
            appendInfoItem(container, createInfoItem('DNS-Prefetch', dnsPrefetchControl, 'fas fa-sync'));
            
            // HTTP/2-Unterstützung (indirekt über Performance-API)
            if (window.performance && window.performance.getEntriesByType) {
                const entries = window.performance.getEntriesByType('resource');
                let http2Support = 'Unbekannt';
                
                if (entries.length > 0 && entries[0].nextHopProtocol) {
                    http2Support = entries.some(entry => entry.nextHopProtocol === 'h2') ? 'Ja' : 'Nein';
                }
                
                appendInfoItem(container, createInfoItem('HTTP/2', http2Support, 'fas fa-rocket'));
            }
        }
        
        // Hilfsfunktion zum Hinzufügen eines Info-Items, wenn es nicht null ist
        function appendInfoItem(container, item) {
            if (item) {
                container.appendChild(item);
            }
        }

        // Speicher-Informationen
        function getMemoryInfo() {
            const container = document.querySelector('#memory-info .info-content');
            container.innerHTML = '';
            
            // Prüfen, ob die Performance API verfügbar ist
            if (window.performance && window.performance.memory) {
                const memory = window.performance.memory;
                const totalJSHeapSize = (memory.totalJSHeapSize / (1024 * 1024)).toFixed(2);
                const usedJSHeapSize = (memory.usedJSHeapSize / (1024 * 1024)).toFixed(2);
                const jsHeapSizeLimit = (memory.jsHeapSizeLimit / (1024 * 1024)).toFixed(2);
                
                appendInfoItem(container, createInfoItem('JS Heap Größe', `${usedJSHeapSize} MB / ${totalJSHeapSize} MB`, 'fas fa-chart-bar'));
                appendInfoItem(container, createInfoItem('JS Heap Limit', `${jsHeapSizeLimit} MB`, 'fas fa-ban'));
            } else {
                appendInfoItem(container, createInfoItem('Speicherinfo', 'Nicht verfügbar in diesem Browser', 'fas fa-times-circle'));
            }
        }
        
        // Batterie-Informationen
        function getBatteryInfo() {
            const container = document.querySelector('#battery-info .info-content');
            container.innerHTML = '';
            
            // Prüfen, ob die Battery API verfügbar ist
            if (navigator.getBattery) {
                navigator.getBattery().then(battery => {
                    const level = Math.round(battery.level * 100);
                    const charging = battery.charging ? 'Ja' : 'Nein';
                    let remainingTime = 'Unbekannt';
                    
                    if (battery.charging && battery.chargingTime !== Infinity) {
                        const hours = Math.floor(battery.chargingTime / 3600);
                        const minutes = Math.floor((battery.chargingTime % 3600) / 60);
                        remainingTime = `${hours}h ${minutes}m bis voll`;
                    } else if (!battery.charging && battery.dischargingTime !== Infinity) {
                        const hours = Math.floor(battery.dischargingTime / 3600);
                        const minutes = Math.floor((battery.dischargingTime % 3600) / 60);
                        remainingTime = `${hours}h ${minutes}m verbleibend`;
                    }
                    
                    appendInfoItem(container, createInfoItem('Ladezustand', `${level}%`, 'fas fa-plug'));
                    appendInfoItem(container, createInfoItem('Wird geladen', charging, 'fas fa-bolt'));
                    appendInfoItem(container, createInfoItem('Verbleibende Zeit', remainingTime, 'fas fa-hourglass-half'));
                    
                    // Event-Listener für Batterieänderungen
                    battery.addEventListener('levelchange', getBatteryInfo);
                    battery.addEventListener('chargingchange', getBatteryInfo);
                    battery.addEventListener('chargingtimechange', getBatteryInfo);
                    battery.addEventListener('dischargingtimechange', getBatteryInfo);
                });
            } else {
                appendInfoItem(container, createInfoItem('Batterieinfo', 'Nicht verfügbar in diesem Browser', 'fas fa-times-circle'));
            }
        }
        
        // Funktionen und API-Verfügbarkeit
        function getFeaturesInfo() {
            const container = document.querySelector('#features-info .info-content');
            container.innerHTML = '';
            
            // Geolocation API
            const geoAvailable = 'geolocation' in navigator;
            appendInfoItem(container, createInfoItem('Geolocation', geoAvailable ? 'Verfügbar' : 'Nicht verfügbar', 'fas fa-map-marker-alt'));
            
            // WebGL
            let webglSupport = 'Nicht verfügbar';
            try {
                const canvas = document.createElement('canvas');
                webglSupport = !!(window.WebGLRenderingContext && 
                    (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))) ? 
                    'Verfügbar' : 'Nicht verfügbar';
            } catch (e) {}
            appendInfoItem(container, createInfoItem('WebGL', webglSupport, 'fas fa-gamepad'));
            
            // WebRTC
            const webrtcSupport = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
            appendInfoItem(container, createInfoItem('WebRTC', webrtcSupport ? 'Verfügbar' : 'Nicht verfügbar', 'fas fa-video'));
            
            // Notifications API
            const notificationsSupport = 'Notification' in window;
            appendInfoItem(container, createInfoItem('Benachrichtigungen', notificationsSupport ? 'Verfügbar' : 'Nicht verfügbar', 'fas fa-bell'));
            
            // Web Audio API
            const audioSupport = 'AudioContext' in window || 'webkitAudioContext' in window;
            appendInfoItem(container, createInfoItem('Web Audio', audioSupport ? 'Verfügbar' : 'Nicht verfügbar', 'fas fa-music'));
            
            // Clipboard API
            const clipboardSupport = navigator.clipboard && navigator.clipboard.writeText;
            appendInfoItem(container, createInfoItem('Clipboard API', clipboardSupport ? 'Verfügbar' : 'Nicht verfügbar', 'fas fa-clipboard'));
            
            // Fullscreen API
            const fullscreenSupport = document.documentElement.requestFullscreen || 
                                     document.documentElement.mozRequestFullScreen || 
                                     document.documentElement.webkitRequestFullscreen || 
                                     document.documentElement.msRequestFullscreen;
            appendInfoItem(container, createInfoItem('Vollbild API', fullscreenSupport ? 'Verfügbar' : 'Nicht verfügbar', 'fas fa-expand'));
        }

        // Lokalisierungsinformationen
        function getLocaleInfo() {
            const container = document.querySelector('#locale-info .info-content');
            container.innerHTML = '';
            
            // Zeitzone
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            appendInfoItem(container, createInfoItem('Zeitzone', timezone, 'fas fa-clock'));
            
            // Aktuelle Zeit und Datum mit automatischer Aktualisierung
            function updateDateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString();
                
                // Formatiertes Datum mit führenden Nullen für Tag und Monat
                const day = String(now.getDate()).padStart(2, '0');
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const year = now.getFullYear();
                const dateString = `${day}.${month}.${year}`;
                
                const timeItem = document.querySelector('#time-value');
                const dateItem = document.querySelector('#date-value');
                
                if (timeItem) {
                    timeItem.textContent = timeString;
                } else {
                    const item = createInfoItem('Lokale Zeit', timeString, 'fas fa-clock');
                    if (item) {
                        item.querySelector('.info-value').id = 'time-value';
                        container.appendChild(item);
                    }
                }
                
                if (dateItem) {
                    dateItem.textContent = dateString;
                } else {
                    const item = createInfoItem('Datum', dateString, 'fas fa-calendar-alt');
                    if (item) {
                        item.querySelector('.info-value').id = 'date-value';
                        container.appendChild(item);
                    }
                }
            }
            
            // Initial setzen und dann alle Sekunde aktualisieren
            updateDateTime();
            setInterval(updateDateTime, 1000);
            
            // Bevorzugte Sprachen
            if (navigator.languages && navigator.languages.length) {
                const languages = navigator.languages.join(', ');
                appendInfoItem(container, createInfoItem('Sprachen', languages, 'fas fa-language'));
            }
            
            // Formatierungsbeispiele
            const numberFormat = new Intl.NumberFormat().format(1234567.89);
            appendInfoItem(container, createInfoItem('Zahlenformat', numberFormat, 'fas fa-sort-numeric-down'));
            
            const currencyFormat = new Intl.NumberFormat(navigator.language, { 
                style: 'currency', 
                currency: 'EUR' 
            }).format(1234.56);
            appendInfoItem(container, createInfoItem('Währungsformat', currencyFormat, 'fas fa-euro-sign'));
        }
        
        // Mediengeräte-Informationen
        function getMediaInfo() {
            const container = document.querySelector('#media-info .info-content');
            container.innerHTML = '';
            
            // Prüfen, ob MediaDevices API verfügbar ist
            if (navigator.mediaDevices) {
                appendInfoItem(container, createInfoItem('MediaDevices API', 'Verfügbar', 'fas fa-camera'));
                
                // Prüfen, ob Geräte abgefragt werden können
                if (navigator.mediaDevices.enumerateDevices) {
                    navigator.mediaDevices.enumerateDevices()
                        .then(devices => {
                            const cameras = devices.filter(device => device.kind === 'videoinput').length;
                            const microphones = devices.filter(device => device.kind === 'audioinput').length;
                            const speakers = devices.filter(device => device.kind === 'audiooutput').length;
                            
                            appendInfoItem(container, createInfoItem('Kameras', cameras.toString(), 'fas fa-camera-retro'));
                            appendInfoItem(container, createInfoItem('Mikrofone', microphones.toString(), 'fas fa-microphone'));
                            appendInfoItem(container, createInfoItem('Lautsprecher', speakers.toString(), 'fas fa-volume-up'));
                        })
                        .catch(() => {
                            appendInfoItem(container, createInfoItem('Gerätezugriff', 'Nicht erlaubt', 'fas fa-ban'));
                        });
                }
            } else {
                appendInfoItem(container, createInfoItem('MediaDevices API', 'Nicht verfügbar', 'fas fa-times-circle'));
            }
            
            // Unterstützte Medienformate
            const audioFormats = ['mp3', 'ogg', 'wav', 'flac', 'aac'];
            const videoFormats = ['mp4', 'webm', 'ogg', 'avi'];
            
            const audio = document.createElement('audio');
            const supportedAudio = audioFormats
                .filter(format => {
                    const canPlay = audio.canPlayType(`audio/${format}`);
                    return canPlay && canPlay !== 'no';
                })
                .join(', ');
            
            const video = document.createElement('video');
            const supportedVideo = videoFormats
                .filter(format => {
                    const canPlay = video.canPlayType(`video/${format}`);
                    return canPlay && canPlay !== 'no';
                })
                .join(', ');
            
            appendInfoItem(container, createInfoItem('Audio-Formate', supportedAudio || 'Keine erkannt', 'fas fa-file-audio'));
            appendInfoItem(container, createInfoItem('Video-Formate', supportedVideo || 'Keine erkannt', 'fas fa-file-video'));
        }
        
        // Sicherheitsinformationen
        function getSecurityInfo() {
            const container = document.querySelector('#security-info .info-content');
            container.innerHTML = '';
            
            // Cookies
            const cookieEnabled = navigator.cookieEnabled;
            appendInfoItem(container, createInfoItem('Cookies', cookieEnabled ? 'Aktiviert' : 'Deaktiviert', 'fas fa-cookie'));
            
            // Referrer Policy
            const referrerPolicy = document.referrer ? 'Verfügbar' : 'Blockiert';
            appendInfoItem(container, createInfoItem('Referrer', referrerPolicy, 'fas fa-link'));
            
            // Permissions API
            if (navigator.permissions) {
                appendInfoItem(container, createInfoItem('Permissions API', 'Verfügbar', 'fas fa-key'));
                
                // Beispiel: Benachrichtigungsberechtigungen prüfen
                navigator.permissions.query({name: 'notifications'})
                    .then(status => {
                        appendInfoItem(container, createInfoItem('Benachrichtigungen', status.state, 'fas fa-bell'));
                    })
                    .catch(() => {});
            }
            
            // Third-Party Cookies
            const thirdPartyCookies = 'Prüfung nicht möglich';
            appendInfoItem(container, createInfoItem('Third-Party Cookies', thirdPartyCookies, 'fas fa-cookie-bite'));
        }
        
        // Performance-Metriken
        function getPerformanceInfo() {
            const container = document.querySelector('#performance-info .info-content');
            container.innerHTML = '';
            
            if (window.performance) {
                // Navigation Timing API
                if (performance.timing) {
                    const timing = performance.timing;
                    const pageLoadTime = timing.loadEventEnd - timing.navigationStart;
                    const dnsTime = timing.domainLookupEnd - timing.domainLookupStart;
                    const tcpTime = timing.connectEnd - timing.connectStart;
                    const domLoadTime = timing.domComplete - timing.domLoading;
                    
                    appendInfoItem(container, createInfoItem('Seitenladezeit', `${pageLoadTime} ms`, 'fas fa-stopwatch'));
                    appendInfoItem(container, createInfoItem('DNS-Auflösung', `${dnsTime} ms`, 'fas fa-search'));
                    appendInfoItem(container, createInfoItem('TCP-Verbindung', `${tcpTime} ms`, 'fas fa-plug'));
                    appendInfoItem(container, createInfoItem('DOM-Ladezeit', `${domLoadTime} ms`, 'fas fa-file-code'));
                }
                
                // Performance Navigation API
                if (performance.navigation) {
                    const navigation = performance.navigation;
                    let navigationType = 'Unbekannt';
                    
                    switch (navigation.type) {
                        case 0: navigationType = 'Direkt/Lesezeichen'; break;
                        case 1: navigationType = 'Neu geladen'; break;
                        case 2: navigationType = 'Zurück/Vorwärts'; break;
                        default: navigationType = 'Andere';
                    }
                    
                    appendInfoItem(container, createInfoItem('Navigationstyp', navigationType, 'fas fa-compass'));
                }
            } else {
                appendInfoItem(container, createInfoItem('Performance API', 'Nicht verfügbar', 'fas fa-times-circle'));
            }
        }
    </script>
</body>
</html> 
