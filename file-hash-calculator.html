<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="File Hash Calculator">
    <meta property="og:description" content="Berechnet Datei-Hashes (MD5, SHA-1, SHA-256).">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/file-hash-calculator">
    <meta property="og:image" content="/assets/og-image.png">
    <title>File Hash Calculator</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --text-color: #1D1D1F;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --secondary-color: #F5F5F7;
            --secondary-text-color: #6E6E73;
            --border-color: #E5E5E5;
            --error-color: #FF3B30;
            --success-color: #34C759;
            --border-radius: 12px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --text-color: #FFFFFF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --secondary-color: #2C2C2E;
                --secondary-text-color: #8E8E93;
                --border-color: #3A3A3C;
                --error-color: #FF453A;
                --success-color: #30D158;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
            }
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            transition: background-color 0.3s ease;
        }
        
        .container {
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .subtitle {
            font-size: 1.1rem;
            color: var(--secondary-text-color);
            margin-bottom: 20px;
        }
        
        .card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-bottom: 24px;
            transition: var(--transition);
        }
        
        .drop-area {
            border: 2px dashed var(--border-color);
            border-radius: 10px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .drop-area.active {
            border-color: var(--primary-color);
            background-color: rgba(0, 122, 255, 0.05);
        }
        
        .file-input {
            display: none;
        }
        
        .button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: opacity 0.3s;
            margin: 8px 8px 8px 0;
        }
        
        .button:hover {
            opacity: 0.9;
        }
        
        .button.danger {
            background-color: var(--error-color);
        }
        
        .button.secondary {
            background-color: var(--secondary-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
        }
        
        .results-list {
            margin-top: 20px;
            border-top: 1px solid var(--border-color);
            padding-top: 20px;
        }
        
        .file-result {
            margin-bottom: 20px;
            padding: 16px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background-color: var(--card-color);
        }
        
        .file-name {
            font-weight: 600;
            margin-bottom: 10px;
            word-break: break-all;
        }
        
        .hash-result {
            margin-bottom: 16px;
        }
        
        .hash-label {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .hash-value {
            font-family: monospace;
            background-color: var(--background-color);
            padding: 8px 12px;
            border-radius: 8px;
            word-break: break-all;
            position: relative;
            padding-right: 80px;
        }
        
        .copy-button {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .copy-button.success {
            background-color: var(--success-color);
        }
        
        .note {
            font-size: 14px;
            color: var(--secondary-text-color);
            margin-top: 20px;
            text-align: center;
        }
        
        .file-count {
            margin-bottom: 16px;
            font-weight: 500;
            text-align: center;
        }
        
        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
            max-width: 375px;
            margin-left: auto;
            margin-right: auto;
            display: flex;
            justify-content: center;
        }
        
        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .back-button a:hover {
            /* background-color: rgba(0, 122, 255, 0.1); */
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>File Hash Calculator</h1>
            <p class="subtitle">Berechnet Datei-Hashes (MD5, SHA-1, SHA-256) direkt in deinem Browser.</p>
        </header>
        
        <div class="card">
            <div id="drop-area" class="drop-area">
                <p>Dateien hierher ziehen, einfügen (Strg+V/⌘+V) oder klicken, um Dateien auszuwählen</p>
                <input type="file" id="fileInput" class="file-input" multiple>
                <div class="button-group">
                    <button class="button" id="selectFileButton">Dateien auswählen</button>
                    <button class="button danger" id="resetButton" style="display: none;">Zurücksetzen</button>
                </div>
            </div>
            
            <div id="fileCount" class="file-count" style="display: none;"></div>
            
            <div id="resultsList" class="results-list" style="display: none;"></div>
            
            <p class="note">Diese Anwendung funktioniert komplett offline. Alle Berechnungen werden lokal in deinem Browser durchgeführt.</p>
        </div>
        
        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropArea = document.getElementById('drop-area');
            const fileInput = document.getElementById('fileInput');
            const selectFileButton = document.getElementById('selectFileButton');
            const resetButton = document.getElementById('resetButton');
            const resultsList = document.getElementById('resultsList');
            const fileCountElement = document.getElementById('fileCount');
            
            let processedFiles = [];
            
            // Event Listener für Drag & Drop
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, () => {
                    dropArea.classList.add('active');
                });
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, () => {
                    dropArea.classList.remove('active');
                });
            });
            
            dropArea.addEventListener('drop', handleDrop);
            
            // Event Listener für Dateiauswahl
            selectFileButton.addEventListener('click', () => {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', handleFileSelect);
            
            // Event Listener für Paste (Einfügen)
            document.addEventListener('paste', handlePaste);
            
            // Event Listener für Reset-Button
            resetButton.addEventListener('click', resetApplication);
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                
                if (files.length > 0) {
                    processFiles(files);
                }
            }
            
            function handleFileSelect(e) {
                const files = e.target.files;
                
                if (files.length > 0) {
                    processFiles(files);
                }
            }
            
            function handlePaste(e) {
                const items = e.clipboardData.items;
                const files = [];
                
                for (let i = 0; i < items.length; i++) {
                    if (items[i].kind === 'file') {
                        const file = items[i].getAsFile();
                        files.push(file);
                    }
                }
                
                if (files.length > 0) {
                    processFiles(files);
                }
            }
            
            function processFiles(files) {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    processFile(file);
                }
            }
            
            function processFile(file) {
                const reader = new FileReader();
                
                reader.onload = function(event) {
                    const content = event.target.result;
                    calculateHashes(file, content);
                };
                
                reader.readAsArrayBuffer(file);
            }
            
            function calculateHashes(file, buffer) {
                // ArrayBuffer zu WordArray konvertieren für CryptoJS
                const wordArray = CryptoJS.lib.WordArray.create(buffer);
                
                // Hashes berechnen
                const md5Hash = CryptoJS.MD5(wordArray).toString();
                const sha1Hash = CryptoJS.SHA1(wordArray).toString();
                const sha256Hash = CryptoJS.SHA256(wordArray).toString();
                
                // Datei-Ergebnis zum Array hinzufügen
                const fileResult = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    md5: md5Hash,
                    sha1: sha1Hash,
                    sha256: sha256Hash
                };
                
                processedFiles.push(fileResult);
                
                // Ergebnisse anzeigen
                displayResults();
                
                // Reset-Button anzeigen
                resetButton.style.display = 'inline-block';
            }
            
            function displayResults() {
                resultsList.innerHTML = '';
                
                // Dateizähler anzeigen
                fileCountElement.textContent = `${processedFiles.length} Datei${processedFiles.length !== 1 ? 'en' : ''} verarbeitet`;
                fileCountElement.style.display = 'block';
                
                // Ergebnisliste anzeigen
                resultsList.style.display = 'block';
                
                // Ergebnisse für jede Datei anzeigen
                processedFiles.forEach((file, index) => {
                    const fileElement = document.createElement('div');
                    fileElement.className = 'file-result';
                    
                    const nameSize = formatBytes(file.size);
                    
                    fileElement.innerHTML = `
                        <div class="file-name">${file.name} (${nameSize})</div>
                        
                        <div class="hash-result">
                            <div class="hash-label">MD5:</div>
                            <div class="hash-value">
                                <span>${file.md5}</span>
                                <button class="copy-button" data-hash="${file.md5}">Kopieren</button>
                            </div>
                        </div>
                        
                        <div class="hash-result">
                            <div class="hash-label">SHA-1:</div>
                            <div class="hash-value">
                                <span>${file.sha1}</span>
                                <button class="copy-button" data-hash="${file.sha1}">Kopieren</button>
                            </div>
                        </div>
                        
                        <div class="hash-result">
                            <div class="hash-label">SHA-256:</div>
                            <div class="hash-value">
                                <span>${file.sha256}</span>
                                <button class="copy-button" data-hash="${file.sha256}">Kopieren</button>
                            </div>
                        </div>
                    `;
                    
                    resultsList.appendChild(fileElement);
                });
                
                // Event Listener für Kopier-Buttons
                const copyButtons = document.querySelectorAll('.copy-button');
                copyButtons.forEach(button => {
                    button.addEventListener('click', copyToClipboard);
                });
            }
            
            function copyToClipboard(e) {
                const hashValue = e.target.getAttribute('data-hash');
                
                if (hashValue) {
                    navigator.clipboard.writeText(hashValue).then(() => {
                        const originalText = e.target.textContent;
                        e.target.textContent = 'Kopiert!';
                        e.target.classList.add('success');
                        setTimeout(() => {
                            e.target.textContent = originalText;
                            e.target.classList.remove('success');
                        }, 1500);
                    });
                }
            }
            
            function resetApplication() {
                // Array zurücksetzen
                processedFiles = [];
                
                // UI zurücksetzen
                resultsList.innerHTML = '';
                resultsList.style.display = 'none';
                fileCountElement.style.display = 'none';
                resetButton.style.display = 'none';
                fileInput.value = '';
            }
            
            function formatBytes(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';
                
                const k = 1024;
                const dm = decimals < 0 ? 0 : decimals;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
                
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                
                return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
            }
        });
    </script>
</body>
</html>
