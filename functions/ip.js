// CloudFlare Pages Function zur Rückgabe der IP-Adresse des Besuchers
export function onRequest(context) {
  const clientIP = context.request.headers.get("cf-connecting-ip") || 
                   context.request.headers.get("x-real-ip") || 
                   "Unbekannt";
  
  // Pr<PERSON><PERSON>, ob es eine IPv6-Adresse ist
  const isIPv6 = clientIP.includes(":");
  
  // IP-Informationen vorbereiten
  const ipInfo = {
    ip: clientIP,
    type: isIPv6 ? "IPv6" : "IPv4"
  };
  
  return new Response(JSON.stringify(ipInfo), {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "https://tools.nyanya.de",
      "Cache-Control": "no-store, no-cache"
    }
  });
} 
