<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GOROOT" url="file://$USER_HOME$/.sdks/go/share/go" />
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Abstraction issuesJava</id>
          </State>
          <State>
            <id>Assignment issuesJava</id>
          </State>
          <State>
            <id>Assignment issuesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Bitwise operation issuesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>CSS</id>
          </State>
          <State>
            <id>CastNumeric issuesJava</id>
          </State>
          <State>
            <id>Class metricsJava</id>
          </State>
          <State>
            <id>Class structureJava</id>
          </State>
          <State>
            <id>ClassNaming conventionsJava</id>
          </State>
          <State>
            <id>Cloning issuesJava</id>
          </State>
          <State>
            <id>Code maturityJava</id>
          </State>
          <State>
            <id>Code quality toolsCSS</id>
          </State>
          <State>
            <id>Code quality toolsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Code smellPHP</id>
          </State>
          <State>
            <id>Code style issuesGo</id>
          </State>
          <State>
            <id>Code style issuesJava</id>
          </State>
          <State>
            <id>Code style issuesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Code stylePHP</id>
          </State>
          <State>
            <id>Compiler issuesJava</id>
          </State>
          <State>
            <id>ComposerPHP</id>
          </State>
          <State>
            <id>Concurrency annotation issuesJava</id>
          </State>
          <State>
            <id>Control flow issuesGo</id>
          </State>
          <State>
            <id>Control flow issuesGroovy</id>
          </State>
          <State>
            <id>Control flow issuesJava</id>
          </State>
          <State>
            <id>Control flow issuesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Control flowPHP</id>
          </State>
          <State>
            <id>Data flowGroovy</id>
          </State>
          <State>
            <id>Data flowJava</id>
          </State>
          <State>
            <id>Declaration redundancyGo</id>
          </State>
          <State>
            <id>Declaration redundancyGo modules</id>
          </State>
          <State>
            <id>Declaration redundancyGroovy</id>
          </State>
          <State>
            <id>Declaration redundancyJava</id>
          </State>
          <State>
            <id>Dependency issuesJava</id>
          </State>
          <State>
            <id>Django</id>
          </State>
          <State>
            <id>ES2015 migration aidsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>EditorConfig</id>
          </State>
          <State>
            <id>EmbeddedPerformanceJava</id>
          </State>
          <State>
            <id>EncapsulationJava</id>
          </State>
          <State>
            <id>Error handlingGroovy</id>
          </State>
          <State>
            <id>Error handlingJava</id>
          </State>
          <State>
            <id>FinalizationJava</id>
          </State>
          <State>
            <id>Flow type checkerJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Function metricsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>General</id>
          </State>
          <State>
            <id>GeneralGo</id>
          </State>
          <State>
            <id>GeneralGo modules</id>
          </State>
          <State>
            <id>GeneralJavaScript and TypeScript</id>
          </State>
          <State>
            <id>GeneralPHP</id>
          </State>
          <State>
            <id>Go</id>
          </State>
          <State>
            <id>Go modules</id>
          </State>
          <State>
            <id>GraphQL</id>
          </State>
          <State>
            <id>Groovy</id>
          </State>
          <State>
            <id>HTML</id>
          </State>
          <State>
            <id>HTTP Client</id>
          </State>
          <State>
            <id>Imports and dependenciesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>ImportsJava</id>
          </State>
          <State>
            <id>Inheritance issuesJava</id>
          </State>
          <State>
            <id>InitializationJava</id>
          </State>
          <State>
            <id>Internationalization</id>
          </State>
          <State>
            <id>InternationalizationJava</id>
          </State>
          <State>
            <id>Invalid elementsCSS</id>
          </State>
          <State>
            <id>JSON and JSON5</id>
          </State>
          <State>
            <id>JUnitJava</id>
          </State>
          <State>
            <id>JVM languages</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Java 10Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 11Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 15Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 16Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 5Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 7Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 8Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 9Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java interop issuesKotlin</id>
          </State>
          <State>
            <id>Java language level issuesJava</id>
          </State>
          <State>
            <id>Java language level migration aidsJava</id>
          </State>
          <State>
            <id>JavaBeans issuesJava</id>
          </State>
          <State>
            <id>JavaScript and TypeScript</id>
          </State>
          <State>
            <id>JavadocJava</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>Less</id>
          </State>
          <State>
            <id>LoggingJava</id>
          </State>
          <State>
            <id>LoggingKotlin</id>
          </State>
          <State>
            <id>Manifest</id>
          </State>
          <State>
            <id>Markdown</id>
          </State>
          <State>
            <id>MemoryJava</id>
          </State>
          <State>
            <id>Method metricsGroovy</id>
          </State>
          <State>
            <id>Method metricsJava</id>
          </State>
          <State>
            <id>MethodNaming conventionsJava</id>
          </State>
          <State>
            <id>MigrationKotlin</id>
          </State>
          <State>
            <id>Modularization issuesJava</id>
          </State>
          <State>
            <id>MongoJS</id>
          </State>
          <State>
            <id>MySQL</id>
          </State>
          <State>
            <id>Naming conventionsGroovy</id>
          </State>
          <State>
            <id>Naming conventionsJava</id>
          </State>
          <State>
            <id>Naming conventionsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Naming conventionsKotlin</id>
          </State>
          <State>
            <id>Nullability problemsProbable bugsJava</id>
          </State>
          <State>
            <id>Numeric issuesJava</id>
          </State>
          <State>
            <id>Oracle</id>
          </State>
          <State>
            <id>Other problemsKotlin</id>
          </State>
          <State>
            <id>OtherGroovy</id>
          </State>
          <State>
            <id>PHP</id>
          </State>
          <State>
            <id>PHPUnitPHP</id>
          </State>
          <State>
            <id>PSR-12Code stylePHP</id>
          </State>
          <State>
            <id>Packaging issuesJava</id>
          </State>
          <State>
            <id>Pattern validation</id>
          </State>
          <State>
            <id>PerformanceJava</id>
          </State>
          <State>
            <id>PortabilityJava</id>
          </State>
          <State>
            <id>Potentially confusing code constructsGroovy</id>
          </State>
          <State>
            <id>Potentially confusing code constructsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Probable bugsGo</id>
          </State>
          <State>
            <id>Probable bugsGroovy</id>
          </State>
          <State>
            <id>Probable bugsJava</id>
          </State>
          <State>
            <id>Probable bugsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Probable bugsKotlin</id>
          </State>
          <State>
            <id>Probable bugsPHP</id>
          </State>
          <State>
            <id>Probable bugsReactKotlin</id>
          </State>
          <State>
            <id>Proofreading</id>
          </State>
          <State>
            <id>Properties files</id>
          </State>
          <State>
            <id>Properties filesJava</id>
          </State>
          <State>
            <id>Python</id>
          </State>
          <State>
            <id>Quality toolsPHP</id>
          </State>
          <State>
            <id>RELAX NG</id>
          </State>
          <State>
            <id>ReactJavaScript and TypeScript</id>
          </State>
          <State>
            <id>ReactKotlin</id>
          </State>
          <State>
            <id>Redundant constructsKotlin</id>
          </State>
          <State>
            <id>Reflective accessJava</id>
          </State>
          <State>
            <id>RegExp</id>
          </State>
          <State>
            <id>Regular expressionsPHP</id>
          </State>
          <State>
            <id>Resource managementJava</id>
          </State>
          <State>
            <id>SQL</id>
          </State>
          <State>
            <id>Sass/SCSS</id>
          </State>
          <State>
            <id>SchemaGraphQL</id>
          </State>
          <State>
            <id>Security</id>
          </State>
          <State>
            <id>SecurityGo</id>
          </State>
          <State>
            <id>SecurityJava</id>
          </State>
          <State>
            <id>Serialization issuesJava</id>
          </State>
          <State>
            <id>Style issuesKotlin</id>
          </State>
          <State>
            <id>StyleGroovy</id>
          </State>
          <State>
            <id>TOML</id>
          </State>
          <State>
            <id>Test frameworksJVM languages</id>
          </State>
          <State>
            <id>Test frameworksJava</id>
          </State>
          <State>
            <id>Threading issuesGroovy</id>
          </State>
          <State>
            <id>Threading issuesJava</id>
          </State>
          <State>
            <id>Try statement issuesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Type compatibilityPHP</id>
          </State>
          <State>
            <id>TypeScriptJavaScript and TypeScript</id>
          </State>
          <State>
            <id>UI form</id>
          </State>
          <State>
            <id>Undefined symbolsPHP</id>
          </State>
          <State>
            <id>Unused symbolsPHP</id>
          </State>
          <State>
            <id>Verbose or redundant code constructsJava</id>
          </State>
          <State>
            <id>Version control</id>
          </State>
          <State>
            <id>VisibilityJava</id>
          </State>
          <State>
            <id>Vue</id>
          </State>
          <State>
            <id>XML</id>
          </State>
          <State>
            <id>XPath</id>
          </State>
          <State>
            <id>YAML</id>
          </State>
          <State>
            <id>toString() issuesJava</id>
          </State>
        </expanded-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="jbr-17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="/etc/profiles/per-user/andi/bin" />
  </component>
</project>