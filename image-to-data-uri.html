<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Image to Data URI">
    <meta property="og:description" content="Konvertiert Bilder in Data-URI-Format zur direkten Einbettung in Websites oder CSS.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/image-to-data-uri">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Image to Data URI</title>
    <style>
        :root {
            --primary-color: #0066cc;
            --success-color: #34c759;
            --background: #f5f7fa;
            --surface: #ffffff;
            --text: #1d1d1f;
            --text-secondary: #6e6e73;
            --border: #d2d2d7;
            --transition: all 0.2s ease;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --success-color: #30D158;
                --background: #1C1C1E;
                --surface: #2C2C2E;
                --text: #FFFFFF;
                --text-secondary: #8E8E93;
                --border: #3A3A3C;
            }
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: var(--background);
            color: var(--text);
            line-height: 1.5;
        }

        h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            color: var(--text);
        }

        .container {
            background-color: var(--surface);
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .drop-zone {
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin: 1.5rem 0;
            background-color: var(--background);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .drop-zone:hover {
            border-color: var(--primary-color);
            background-color: #f0f7ff;
        }

        .drop-zone.dragover {
            border-color: var(--primary-color);
            background-color: #f0f7ff;
            transform: scale(1.01);
        }

        .drop-zone.has-image {
            padding: 0;
            overflow: hidden;
        }

        .drop-zone.has-image .drop-zone-text {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.8rem;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .drop-zone.has-image:hover .drop-zone-text {
            opacity: 1;
        }

        .drop-zone p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin: 0;
        }

        .preview {
            max-width: 100%;
            max-height: 400px;
            display: none;
            object-fit: contain;
        }

        .result-container {
            margin: 1.5rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        textarea {
            width: 100%;
            height: 120px;
            margin: 0;
            padding: 1rem;
            border: 1px solid var(--border);
            border-radius: 8px;
            resize: none;
            font-family: inherit;
            font-size: 0.9rem;
            line-height: 1.4;
            color: var(--text);
            background-color: var(--background);
            box-sizing: border-box;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        button:hover {
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
        }

        button.success {
            background-color: var(--success-color);
        }

        button.success:hover {
            background-color: color-mix(in srgb, var(--success-color) 85%, black);
        }

        button svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        button.success svg {
            animation: checkmark 0.2s ease-in-out;
        }

        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .hidden {
            display: none;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image to Data URI Converter</h1>
        
        <div class="drop-zone" id="dropZone">
            <img id="preview" class="preview">
            <p class="drop-zone-text">Paste image, drag & drop, or click to upload</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <div class="result-container">
            <textarea id="output" readonly placeholder="Data URI will be shown here..."></textarea>
            <button id="copyButton">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
                Copy to clipboard
            </button>
        </div>
    </div>

    <div class="back-button">
        <a href="index.html">← Zurück zur Übersicht</a>
    </div>

    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const preview = document.getElementById('preview');
        const output = document.getElementById('output');
        const copyButton = document.getElementById('copyButton');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('image/')) {
                handleFile(file);
            }
        });

        dropZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        });

        document.addEventListener('paste', (e) => {
            const items = e.clipboardData.items;
            for (let item of items) {
                if (item.type.startsWith('image/')) {
                    const file = item.getAsFile();
                    handleFile(file);
                    break;
                }
            }
        });

        function handleFile(file) {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const dataUri = e.target.result;
                preview.src = dataUri;
                preview.style.display = 'block';
                output.value = dataUri;
                dropZone.classList.add('has-image');
            };
            
            reader.readAsDataURL(file);
        }

        // Copy button functionality
        copyButton.addEventListener('click', async () => {
            try {
                // Try using the Clipboard API first
                try {
                    await navigator.clipboard.writeText(output.value);
                } catch (clipboardErr) {
                    // Fallback for browsers that don't support Clipboard API (like Firefox for Android)
                    const textarea = document.createElement('textarea');
                    textarea.value = output.value;
                    textarea.style.position = 'fixed';  // Avoid scrolling to bottom
                    textarea.style.opacity = '0';
                    document.body.appendChild(textarea);
                    textarea.focus();
                    textarea.select();
                    
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textarea);
                    
                    if (!successful) {
                        throw new Error('Fallback clipboard copy failed');
                    }
                }
                
                const originalText = copyButton.textContent;
                const originalIcon = copyButton.querySelector('svg').outerHTML;
                
                copyButton.classList.add('success');
                copyButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                    </svg>
                    Copied!
                `;
                
                setTimeout(() => {
                    copyButton.classList.remove('success');
                    copyButton.innerHTML = originalIcon + originalText;
                }, 2000);
            } catch (err) {
                alert('Error beim Kopieren: ' + err);
            }
        });
    </script>
</body>
</html>
