<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Github Changelog">
    <meta property="og:description" content="Zeigt Changelogs von GitHub Releases an und ermöglicht das einfache Kopieren.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/github-changelog">
    <meta property="og:image" content="/assets/og-image.png">
    <title>GitHub Changelog</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-color: rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --success-color: #34C759;
            --error-color: #FF3B30;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --border-color: rgba(255, 255, 255, 0.1);
                --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                --success-color: #30D158;
                --error-color: #FF453A;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 20px;
            min-height: 100vh;
            line-height: 1.5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        header {
            margin-bottom: 30px;
            text-align: center;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .subtitle {
            color: var(--secondary-text-color);
            margin-bottom: 20px;
        }

        .card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-bottom: 24px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        input[type="url"] {
            width: 100%;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--card-color);
            color: var(--text-color);
            font-size: 16px;
            transition: all 0.2s ease;
        }

        input[type="url"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }

        .btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn:hover {
            /* opacity: 0.9; */
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
        }

        .btn:active {
            transform: scale(0.98);
        }

        .btn-copy {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-copy:hover {
            /* background-color: rgba(0, 122, 255, 0.1); */
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }

        .btn-copy.success {
            background-color: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .actions {
            display: flex;
            gap: 10px;
        }

        #result {
            margin-top: 30px;
            display: none;
        }

        #changelog {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 20px;
            white-space: pre-wrap;
            overflow-wrap: break-word;
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.6;
        }

        .repo-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .repo-logo {
            margin-right: 15px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .release-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .release-tag {
            display: inline-block;
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .release-title {
            font-size: 22px;
            margin-bottom: 5px;
        }

        .release-date {
            color: var(--secondary-text-color);
            font-size: 14px;
        }

        .spinner {
            display: none;
            width: 30px;
            height: 30px;
            margin: 0 auto;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spinner 0.8s linear infinite;
        }

        @keyframes spinner {
            to {
                transform: rotate(360deg);
            }
        }

        .notification {
            padding: 15px;
            margin-bottom: 20px;
            margin-top: 15px;
            border-radius: 8px;
            display: none;
        }

        .notification.error {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(255, 59, 48, 0.2);
        }

        .notification.success {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(52, 199, 89, 0.2);
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px; /* Use consistent 8px radius */
            transition: all 0.2s ease; /* Use existing transition */
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }

        @media (max-width: 600px) {
            body {
                padding: 15px;
            }

            .card {
                padding: 20px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>GitHub Changelog Viewer</h1>
            <p class="subtitle">Zeigt den Changelog einer GitHub-Release-Version an</p>
        </header>

        <div class="card">
            <div class="input-group">
                <label for="repoUrl">GitHub Release URL eingeben</label>
                <input type="url" id="repoUrl" placeholder="z.B. https://github.com/owner/repo/releases/tag/v1.0.0" autocomplete="off" autofocus>
            </div>
            <div class="actions">
                <button id="fetchBtn" class="btn">Changelog abrufen</button>
            </div>
            <div id="errorNotification" class="notification error"></div>
        </div>

        <div id="spinner" class="spinner"></div>

        <div id="result">
            <div id="releaseInfo">
                <div class="repo-info">
                    <img id="repoLogo" class="repo-logo" src="" alt="Repository Logo">
                    <div>
                        <h3 id="repoName"></h3>
                    </div>
                </div>
                <div class="release-header">
                    <span id="releaseTag" class="release-tag"></span>
                    <h2 id="releaseTitle" class="release-title"></h2>
                    <div id="releaseDate" class="release-date"></div>
                </div>
            </div>
            <div id="changelog"></div>
            <div class="actions">
                <button id="copyBtn" class="btn btn-copy">In Zwischenablage kopieren</button>
            </div>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        const repoUrlInput = document.getElementById('repoUrl');
        const fetchBtn = document.getElementById('fetchBtn');
        const spinner = document.getElementById('spinner');
        const result = document.getElementById('result');
        const changelog = document.getElementById('changelog');
        const copyBtn = document.getElementById('copyBtn');
        const errorNotification = document.getElementById('errorNotification');
        const repoLogo = document.getElementById('repoLogo');
        const repoName = document.getElementById('repoName');
        const releaseTag = document.getElementById('releaseTag');
        const releaseTitle = document.getElementById('releaseTitle');
        const releaseDate = document.getElementById('releaseDate');

        function parseGitHubReleaseUrl(url) {
            try {
                const regex = /github\.com\/([^\/]+)\/([^\/]+)\/releases\/tag\/([^\/]+)/;
                const match = url.match(regex);
                
                if (!match) {
                    throw new Error("Keine gültige GitHub Release URL");
                }
                
                return {
                    owner: match[1],
                    repo: match[2],
                    tag: match[3]
                };
            } catch (error) {
                throw new Error("Konnte die URL nicht verarbeiten");
            }
        }

        async function fetchReleaseInfo(owner, repo, tag) {
            try {
                const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/releases/tags/${tag}`);
                
                if (!response.ok) {
                    if (response.status === 404) {
                        throw new Error("Release nicht gefunden");
                    }
                    if (response.status === 403) {
                        throw new Error("API-Limit überschritten. Bitte später erneut versuchen");
                    }
                    throw new Error("Fehler beim Abrufen der Daten");
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('de-DE', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        fetchBtn.addEventListener('click', async () => {
            const url = repoUrlInput.value.trim();
            
            if (!url) {
                showError("Bitte gib eine GitHub Release URL ein");
                return;
            }
            
            errorNotification.style.display = 'none';
            result.style.display = 'none';
            spinner.style.display = 'block';
            
            try {
                const { owner, repo, tag } = parseGitHubReleaseUrl(url);
                const releaseData = await fetchReleaseInfo(owner, repo, tag);
                
                // Repo-Infos setzen
                repoLogo.src = releaseData.author.avatar_url;
                repoName.textContent = `${owner}/${repo}`;
                
                // Release-Infos setzen
                releaseTag.textContent = tag;
                releaseTitle.textContent = releaseData.name || tag;
                releaseDate.textContent = `Veröffentlicht am ${formatDate(releaseData.published_at)}`;
                
                // Changelog-Body setzen
                changelog.innerHTML = releaseData.body;
                
                // Anzeigen
                spinner.style.display = 'none';
                result.style.display = 'block';
            } catch (error) {
                spinner.style.display = 'none';
                showError(error.message);
            }
        });

        repoUrlInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                fetchBtn.click();
            }
        });

        copyBtn.addEventListener('click', () => {
            try {
                // Get the text content and remove extra blank lines between list items
                const content = changelog.textContent;
                let cleanedContent = content.replace(/\n\s*\n(\s*[-*]\s)/g, '\n$1');
                
                // Replace Markdown headings with <strong> tags
                cleanedContent = cleanedContent.replace(/^#{1,6}\s+(.*?)$/gm, '<strong>$1</strong>');
                
                // Replace Markdown links with HTML links
                cleanedContent = cleanedContent.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
                
                navigator.clipboard.writeText(cleanedContent);
                const originalText = copyBtn.textContent;
                copyBtn.textContent = 'Kopiert!';
                copyBtn.classList.add('success');
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.classList.remove('success');
                }, 1500);
            } catch (err) {
                showError("Kopieren in die Zwischenablage fehlgeschlagen");
            }
        });

        function showError(message) {
            errorNotification.textContent = message;
            errorNotification.style.display = 'block';
        }
    </script>
</body>
</html> 
