<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Zahlensysteme-Konverter">
    <meta property="og:description" content="Konvertiert Dezimalzahlen in andere Zahlensysteme wie Hexadezimal und Binär.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/decimal-to-hex">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Zahlensysteme-Konverter</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css" 
          integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a" 
          crossorigin="anonymous">
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 16px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --card-padding: 24px;
            --transition: all 0.3s ease;
            --accent-color: #5E5CE6;
            --success-color: #34C759;
            --error-color: #FF3B30;
            --card-hover-transform: translateY(-5px);
            --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            --info-value-bg: rgba(0, 0, 0, 0.02);
            --border-color: rgba(0, 0, 0, 0.05);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                --accent-color: #5E5CE6;
                --success-color: #30D158;
                --error-color: #FF453A;
                --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
                --info-value-bg: rgba(255, 255, 255, 0.05);
                --border-color: rgba(255, 255, 255, 0.1);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 16px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 4px;
        }

        header {
            margin-bottom: 40px;
            text-align: center;
            padding: 0 8px;
        }

        h1 {
            font-size: clamp(1.75rem, 5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-color);
            word-wrap: break-word;
        }

        .subtitle {
            font-size: clamp(1rem, 3vw, 1.2rem);
            color: var(--secondary-text-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .converter-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: var(--card-padding);
            margin-bottom: 24px;
        }

        .input-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }

        input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background-color: var(--info-value-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: var(--transition);
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        button:hover {
            opacity: 0.9;
        }

        .result-container {
            margin-top: 30px;
        }

        .result-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .result-value {
            font-size: 24px;
            font-family: monospace;
            padding: 12px 16px;
            background-color: var(--info-value-bg);
            border-radius: var(--border-radius);
            overflow-wrap: break-word;
            border: 1px solid var(--border-color);
            margin-bottom: 15px;
        }

        .error {
            color: var(--error-color);
            background-color: rgba(255, 59, 48, 0.1);
            padding: 12px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .hidden {
            display: none;
        }

        .additional-info {
            margin-top: 40px;
            padding: 20px;
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .additional-info h2 {
            color: var(--text-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .additional-info p {
            color: var(--secondary-text-color);
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fa-solid fa-calculator"></i> Zahlensysteme-Konverter</h1>
            <p class="subtitle">Konvertiert Dezimalzahlen in andere Zahlensysteme.</p>
        </header>

        <div class="converter-card">
            <div class="input-group">
                <label for="decimal-input">Dezimalzahl eingeben:</label>
                <input type="number" id="decimal-input" placeholder="Dezimalzahl (z.B. 255)" autocomplete="off">
            </div>

            <button id="convert-btn">Konvertieren</button>
            
            <div id="error-message" class="error hidden"></div>

            <div id="result-container" class="result-container hidden">
                <h2 class="result-title">Hexadezimal</h2>
                <div id="hex-output" class="result-value"></div>
                
                <h2 class="result-title">Binär</h2>
                <div id="binary-output" class="result-value"></div>
            </div>
        </div>
        
        <div class="additional-info">
            <h2>Über Zahlensysteme</h2>
            <p>Das Hexadezimalsystem ist ein Zahlensystem mit der Basis 16, das die Ziffern 0-9 und die Buchstaben A-F verwendet, wobei A=10, B=11, ..., F=15 entspricht.</p>
            <p>Das Binärsystem ist ein Zahlensystem mit der Basis 2, das nur die Ziffern 0 und 1 verwendet. Es ist die Grundlage für die digitale Datenverarbeitung in Computern.</p>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const decimalInput = document.getElementById('decimal-input');
            const convertBtn = document.getElementById('convert-btn');
            const resultContainer = document.getElementById('result-container');
            const hexOutput = document.getElementById('hex-output');
            const binaryOutput = document.getElementById('binary-output');
            const errorMessage = document.getElementById('error-message');

            decimalInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    convertToHex();
                }
            });

            convertBtn.addEventListener('click', convertToHex);

            function convertToHex() {
                // Get the decimal value and validate
                const decimalValue = decimalInput.value.trim();
                
                if (!decimalValue) {
                    showError('Bitte gib eine Dezimalzahl ein.');
                    return;
                }

                try {
                    const num = parseInt(decimalValue, 10);
                    
                    if (isNaN(num)) {
                        showError('Ungültige Eingabe. Bitte gib eine gültige Dezimalzahl ein.');
                        return;
                    }

                    if (num < 0) {
                        showError('Negative Zahlen werden nicht unterstützt.');
                        return;
                    }

                    if (num > Number.MAX_SAFE_INTEGER) {
                        showError('Die Zahl ist zu groß.');
                        return;
                    }

                    // Convert to hexadecimal
                    const hexValue = num.toString(16).toUpperCase();
                    
                    // Convert to binary with proper formatting
                    let binaryValue = num.toString(2);
                    // Pad with zeros to make it divisible by 4 for better visualization
                    const padding = Math.ceil(binaryValue.length / 4) * 4;
                    binaryValue = binaryValue.padStart(padding, '0');
                    
                    // Group binary digits in groups of 4 for better readability
                    let formattedBinary = '';
                    for (let i = 0; i < binaryValue.length; i += 4) {
                        formattedBinary += binaryValue.slice(i, i + 4) + ' ';
                    }
                    
                    // Display the results
                    hexOutput.textContent = `0x${hexValue}`;
                    binaryOutput.textContent = formattedBinary.trim();
                    
                    resultContainer.classList.remove('hidden');
                    errorMessage.classList.add('hidden');
                } catch (error) {
                    showError('Fehler bei der Umwandlung');
                    console.error(error);
                }
            }
            
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.classList.remove('hidden');
                resultContainer.classList.add('hidden');
            }

            // Initialize with focus on input
            decimalInput.focus();
            
            // Convert on page load if there's a value
            if (decimalInput.value) {
                convertToHex();
            }
        });
    </script>
</body>
</html> 

