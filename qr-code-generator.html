<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Generator</title>
    <meta property="og:title" content="QR Code Generator">
    <meta property="og:description" content="Erstelle QR Codes für URLs oder Text.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/qr-code-generator">
    <meta property="og:image" content="/assets/og-image.png">
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css">
    <script src="/assets/qrcode.min.js"></script>
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 16px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --card-padding: 24px;
            --transition: all 0.3s ease;
            --accent-color: #5E5CE6;
            --success-color: #34C759;
            --error-color: #FF3B30;
            --card-hover-transform: translateY(-5px);
            --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            --info-value-bg: rgba(0, 0, 0, 0.02);
            --border-color: rgba(0, 0, 0, 0.05);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                --accent-color: #5E5CE6;
                --success-color: #30D158;
                --error-color: #FF453A;
                --card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
                --info-value-bg: rgba(255, 255, 255, 0.05);
                --border-color: rgba(255, 255, 255, 0.1);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 16px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 4px;
        }

        header {
            margin-bottom: 40px;
            text-align: center;
            padding: 0 8px;
        }

        h1 {
            font-size: clamp(1.75rem, 5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-color);
            word-wrap: break-word;
        }

        .subtitle {
            font-size: clamp(1rem, 3vw, 1.2rem);
            color: var(--secondary-text-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .qr-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: var(--card-padding);
            margin-bottom: 24px;
        }

        .input-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }

        input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background-color: var(--info-value-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: var(--transition);
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        button:hover {
            opacity: 0.9;
        }

        .result-container {
            margin-top: 30px;
        }

        .result-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        #qrcode {
            margin-top: 24px;
            display: none;
            justify-content: center;
            align-items: center;
            background-color: white;
            padding: 10px;
            border-radius: var(--border-radius);
            width: fit-content;
            margin-left: auto;
            margin-right: auto;
            box-shadow: var(--shadow);
        }

        #qrcode img {
            display: block;
            max-width: 100%;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            #qrcode {
                border: 1px solid var(--border-color);
            }
        }

        .error {
            color: var(--error-color);
            background-color: rgba(255, 59, 48, 0.1);
            padding: 12px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-qrcode"></i> QR Code Generator</h1>
            <p class="subtitle">Erstelle einfach QR Codes für URLs oder Text.</p>
        </header>

        <div class="qr-card">
            <div class="input-group">
                <label for="text-input">Text oder URL eingeben:</label>
                <input type="text" id="text-input" placeholder="z.B. https://www.example.com">
            </div>

            <button id="generate-button">QR Code generieren</button>

            <div id="error-message-container" class="error hidden"></div>

            <div class="result-container">
                <div id="qrcode"></div>
            </div>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>
    <script>
        const textInput = document.getElementById('text-input');
        const generateButton = document.getElementById('generate-button');

        function generateQrCode() {
            const text = textInput.value.trim();
            const errorMessageContainer = document.getElementById('error-message-container');
            const qrcodeContainer = document.getElementById('qrcode');
            
            qrcodeContainer.innerHTML = '';
            errorMessageContainer.classList.add('hidden');
            
            if (!text) {
                errorMessageContainer.textContent = 'Bitte gib einen Text oder eine URL ein.';
                errorMessageContainer.classList.remove('hidden');
                return;
            }
            
            try {
                qrcodeContainer.style.display = 'flex';
                
                new QRCode(qrcodeContainer, {
                    text: text,
                    width: 200,
                    height: 200,
                    colorDark: '#000000',
                    colorLight: '#ffffff',
                    correctLevel: QRCode.CorrectLevel.H
                });
            } catch (error) {
                errorMessageContainer.textContent = 'Fehler beim Generieren des QR Codes.';
                errorMessageContainer.classList.remove('hidden');
                qrcodeContainer.style.display = 'none';
            }
        }

        generateButton.addEventListener('click', generateQrCode);

        textInput.addEventListener('keypress', function (event) {
            if (event.key === 'Enter') {
                generateQrCode();
            }
        });

        const params = new URLSearchParams(window.location.search);
        const initialText = params.get('text');
        if (initialText) {
            textInput.value = initialText;
            generateQrCode();
        }
    </script>
</body>

</html>
