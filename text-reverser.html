<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Text Reverser">
    <meta property="og:description" content="Kehrt eingegebenen Text um - von vorne nach hinten oder umgekehrt.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/text-reverser">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Text Reverser</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #FFFFFF;
            --secondary-background: #F5F5F7;
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #1C1C1E;
                --secondary-background: #2C2C2E;
                --text-primary: #FFFFFF;
                --text-secondary: #8E8E93;
            }
        }

        body {
            font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            display: flex;
            justify-content: center;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 1.6rem;
        }

        h1 {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0;
            letter-spacing: -0.5px;
        }

        textarea {
            width: 100%;
            height: 150px;
            padding: 1rem;
            font-size: 1.1rem;
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 12px;
            background-color: var(--secondary-background);
            color: var(--text-primary);
            transition: all 0.2s ease;
            resize: vertical;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,122,255,0.1);
        }

        button {
            padding: 0.8rem 1.6rem;
            font-size: 1.1rem;
            font-weight: 500;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            align-self: flex-start;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        button:hover {
            /* background-color: #0063CC; */
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
            /* transform: translateY(-1px); Removed */
        }

        button:active {
            transform: translateY(0);
        }

        .output-label {
            font-size: 0.95rem;
            color: var(--text-secondary);
            margin-bottom: -0.8rem;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px; /* Use consistent 8px radius */
            transition: all 0.2s ease; /* Use existing transition */
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Text umkehren</h1>
        <textarea id="input" placeholder="Text eingeben..." autofocus></textarea>
        
        <button onclick="reverseText()">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 2l4 4-4 4"/>
                <path d="M3 11v-1a4 4 0 0 1 4-4h14M7 22l-4-4 4-4"/>
                <path d="M21 13v1a4 4 0 0 1-4 4H3"/>
            </svg>
            Umkehren
        </button>

        <div class="output-label">Resultat</div>
        <textarea id="output" readonly></textarea>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        function reverseText() {
            const input = document.getElementById('input');
            const output = document.getElementById('output');
            
            const reversed = input.value.split('').reverse().join('');
            output.value = reversed;
            
            // Sanfte Bestätigungsanimation
            output.style.transform = 'translateX(8px)';
            output.style.transition = 'transform 0.15s ease-out';
            setTimeout(() => {
                output.style.transform = 'translateX(0)';
                output.style.transition = 'transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            }, 150);
        }
    </script>
</body>
</html>
