<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Tester</title>
    <meta property="og:title" content="Favicon Tester">
    <meta property="og:description" content="Lade und teste deine Favicons!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/favicon-tester">
    <meta property="og:image" content="/assets/og-image.png">
    <link id="favicon" rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='%234f46e5'/><text x='16' y='20' font-family='Arial' font-size='16' fill='white' text-anchor='middle'>F</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px 20px;
            margin: 30px 0;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.15);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-subtext {
            font-size: 0.9em;
            color: #999;
        }

        #fileInput {
            display: none;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #5a6268);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .navigator {
            display: none;
            margin: 30px 0;
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            position: relative;
        }

        .navigator.active {
            display: block;
        }

        .nav-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-btn:hover:not(:disabled) {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }

        .favicon-counter {
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            color: #333;
            min-width: 100px;
        }

        .current-favicon {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .current-favicon img {
            width: 64px;
            height: 64px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .favicon-info {
            text-align: left;
            color: #333;
        }

        .favicon-info h3 {
            margin: 0 0 5px 0;
            color: #667eea;
        }

        .favicon-info p {
            margin: 2px 0;
            font-size: 0.9em;
            color: #666;
        }

        .favicon-gallery {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 15px;
        }

        .favicon-gallery.active {
            display: block;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .gallery-item {
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }

        .gallery-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .gallery-item:hover .delete-btn {
            opacity: 1;
        }

        .gallery-item.active {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.2);
        }

        .gallery-item img {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            margin-bottom: 5px;
        }

        .gallery-item .name {
            font-size: 0.8em;
            color: #666;
            word-break: break-all;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .delete-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .info {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            font-size: 0.9em;
            color: #666;
        }

        .keyboard-hint {
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            color: #666;
        }
        .back-button {
            margin-top: 24px;
            text-align: center;
        }
        .back-button a {
            background: #007AFF;
            color: #FFFFFF;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Favicon-Tester</h1>
        <p>Lade deine Favicons hoch und vergleiche sie schnell!</p>

        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📁</div>
            <div class="upload-text">Favicons hier ablegen oder klicken</div>
            <div class="upload-subtext">Unterstützt: ICO, PNG, SVG, JPG (max. 5MB pro Datei)</div>
        </div>

        <input type="file" id="fileInput" accept=".ico,.png,.svg,.jpg,.jpeg" multiple />

        <button class="btn" onclick="document.getElementById('fileInput').click()">
            Dateien auswählen
        </button>

        <button class="btn btn-danger" onclick="deleteCurrent()" id="deleteBtn" style="display: none;">
            Aktuelles löschen
        </button>

        <button class="btn btn-danger" onclick="clearAll()">
            Alle löschen
        </button>

        <div class="navigator" id="navigator">
            <div class="nav-controls">
                <button class="nav-btn" id="prevBtn" onclick="previousFavicon()">‹</button>
                <div class="favicon-counter" id="counter">0 / 0</div>
                <button class="nav-btn" id="nextBtn" onclick="nextFavicon()">›</button>
            </div>

            <div class="current-favicon" id="currentFavicon">
                <p>Lade Favicons hoch, um sie zu vergleichen!</p>
            </div>
        </div>

        <div class="favicon-gallery" id="gallery">
            <h3>Favicon Galerie</h3>
            <div class="gallery-grid" id="galleryGrid"></div>
        </div>

        <div class="info">
            <strong>Tipps:</strong><br>
            • Verwende ← → Pfeiltasten zum schnellen Durchblättern<br>
            • Klick auf ein Favicon in der Galerie zum direkten Wechsel<br>
            • Lösche einzelne Favicons mit dem "Aktuelles löschen" Button<br>
            • Das aktuelle Favicon siehst du im Browser-Tab<br>
            • Optimal sind 32×32px oder 16×16px Bilder
        </div>
    </div>

    <div class="back-button">
        <a href="index.html">← Zurück zur Übersicht</a>
    </div>

    <script>
        let favicons = [];
        let currentIndex = 0;

        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const navigator = document.getElementById('navigator');
        const currentFavicon = document.getElementById('currentFavicon');
        const counter = document.getElementById('counter');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const deleteBtn = document.getElementById('deleteBtn');
        const gallery = document.getElementById('gallery');
        const galleryGrid = document.getElementById('galleryGrid');
        const faviconLink = document.getElementById('favicon');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        fileInput.addEventListener('change', handleFileSelect);

        document.addEventListener('keydown', (e) => {
            if (favicons.length === 0) return;

            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                previousFavicon();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                nextFavicon();
            }
        });

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            files.forEach(file => {
                if (file.size > 5 * 1024 * 1024) {
                    alert(`Datei ${file.name} ist zu groß! Maximum: 5MB`);
                    return;
                }

                const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/svg+xml', 'image/jpeg', 'image/jpg'];
                if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.ico')) {
                    alert(`Dateityp von ${file.name} nicht unterstützt!`);
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const faviconData = {
                        name: file.name,
                        dataUrl: e.target.result,
                        mimeType: file.type,
                        size: file.size
                    };

                    favicons.push(faviconData);
                    updateDisplay();

                    if (favicons.length === 1) {
                        currentIndex = 0;
                        showFavicon(0);
                    }
                };
                reader.readAsDataURL(file);
            });
        }

        function updateDisplay() {
            if (favicons.length === 0) {
                navigator.classList.remove('active');
                gallery.classList.remove('active');
                deleteBtn.style.display = 'none';
                return;
            }

            navigator.classList.add('active');
            gallery.classList.add('active');
            deleteBtn.style.display = 'inline-block';
            counter.textContent = `${currentIndex + 1} / ${favicons.length}`;

            prevBtn.disabled = currentIndex === 0;
            nextBtn.disabled = currentIndex === favicons.length - 1;

            updateGallery();
        }

        function showFavicon(index) {
            if (index < 0 || index >= favicons.length) return;

            currentIndex = index;
            const favicon = favicons[index];

            setFavicon(favicon.dataUrl, favicon.mimeType);

            const sizeKB = (favicon.size / 1024).toFixed(1);
            currentFavicon.innerHTML = `
                <img src="${favicon.dataUrl}" alt="Current Favicon">
                <div class="favicon-info">
                    <h3>${favicon.name}</h3>
                    <p>Typ: ${favicon.mimeType}</p>
                    <p>Größe: ${sizeKB} KB</p>
                    <p>Index: ${index + 1} / ${favicons.length}</p>
                </div>
            `;

            updateDisplay();
        }

        function setFavicon(dataUrl, mimeType) {
            faviconLink.href = dataUrl;
            if (mimeType === 'image/svg+xml') {
                faviconLink.type = 'image/svg+xml';
            } else if (mimeType === 'image/png') {
                faviconLink.type = 'image/png';
            } else {
                faviconLink.type = 'image/x-icon';
            }
        }

        function previousFavicon() {
            if (currentIndex > 0) {
                showFavicon(currentIndex - 1);
            }
        }

        function nextFavicon() {
            if (currentIndex < favicons.length - 1) {
                showFavicon(currentIndex + 1);
            }
        }

        function updateGallery() {
            galleryGrid.innerHTML = '';
            favicons.forEach((favicon, index) => {
                const item = document.createElement('div');
                item.className = `gallery-item ${index === currentIndex ? 'active' : ''}`;

                item.innerHTML = `
                    <button class="delete-btn" onclick="deleteByIndex(${index})" title="Löschen">×</button>
                    <img src="${favicon.dataUrl}" alt="${favicon.name}">
                    <div class="name">${favicon.name}</div>
                `;

                item.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('delete-btn')) {
                        showFavicon(index);
                    }
                });

                galleryGrid.appendChild(item);
            });
        }

        function deleteByIndex(index) {
            if (index < 0 || index >= favicons.length) return;

            favicons.splice(index, 1);

            if (favicons.length === 0) {
                resetToDefault();
                return;
            }

            if (currentIndex >= favicons.length) {
                currentIndex = favicons.length - 1;
            } else if (index <= currentIndex && currentIndex > 0) {
                currentIndex--;
            }

            showFavicon(currentIndex);
        }

        function deleteCurrent() {
            if (favicons.length === 0) return;

            favicons.splice(currentIndex, 1);

            if (favicons.length === 0) {
                resetToDefault();
                return;
            }

            if (currentIndex >= favicons.length) {
                currentIndex = favicons.length - 1;
            }

            showFavicon(currentIndex);
        }

        function resetToDefault() {
            currentIndex = 0;
            const defaultFavicon = "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='%234f46e5'/><text x='16' y='20' font-family='Arial' font-size='16' fill='white' text-anchor='middle'>F</text></svg>";
            faviconLink.href = defaultFavicon;
            faviconLink.type = 'image/svg+xml';

            currentFavicon.innerHTML = '<p>Lade Favicons hoch, um sie zu vergleichen!</p>';
            updateDisplay();
            updateGallery();
        }

        function clearAll() {
            if (favicons.length === 0) return;

            if (confirm('Alle Favicons löschen?')) {
                favicons = [];
                resetToDefault();
                fileInput.value = '';
                updateGallery();
            }
        }
    </script>
</body>
</html>
