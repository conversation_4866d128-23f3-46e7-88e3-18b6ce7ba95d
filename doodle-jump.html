<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Doodle Jump Klon">
    <meta property="og:description" content="Springe auf Plattformen und erreiche neue Höhen!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/doodle-jump">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Doodle Jump</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 12px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
            
            /* Game specific colors */
            --platform-color: #96E6B3;
            --platform-fragile-color: #E67C5F;
            --platform-broken-color: #F65E3B;
            --doodle-color: #89D2DC;
            --score-bg: #BBADA0;
            --score-text: #FFFFFF;
            --game-background: #F3F8FF;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                
                /* Game specific colors for dark mode */
                --platform-color: #73A789;
                --platform-fragile-color: #E67255;
                --platform-broken-color: #E65E3B;
                --doodle-color: #5CA3AD;
                --score-bg: #4A4A4E;
                --score-text: #FFFFFF;
                --game-background: #252D3A;
            }
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            width: 100%;
            max-width: 375px;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-color);
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: var(--secondary-text-color);
            margin-bottom: 20px;
        }

        .score-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            width: 100%;
            max-width: 375px;
        }

        .score-box {
            background-color: var(--score-bg);
            color: var(--score-text);
            border-radius: var(--border-radius);
            padding: 10px 15px;
            font-weight: bold;
            box-shadow: var(--shadow);
            min-width: 120px;
            text-align: center;
            flex-grow: 1;
            margin: 0 5px;
        }
        
        .score-box:first-child {
            margin-left: 0;
        }
        
        .score-box:last-child {
            margin-right: 0;
        }

        .game-container {
            position: relative;
            width: 375px;
            max-width: 100%;
            height: 600px;
            margin: 0 auto;
            overflow: hidden;
            background-color: var(--game-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: 1px solid var(--secondary-text-color);
        }

        #game {
            width: 100%;
            height: 100%;
            position: relative;
        }

        #start-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10;
        }

        #game-over {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10;
        }

        .btn {
            padding: 12px 24px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.1s;
            margin-top: 16px;
        }

        .btn:hover {
            /* opacity: 0.9; */
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
        }

        .btn:active {
            transform: scale(0.98);
        }

        .controls {
            max-width: 375px;
            width: 100%;
            margin: 24px auto 0;
            text-align: center;
            padding: 15px 20px;
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        
        .controls h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }

        .controls p {
            color: var(--secondary-text-color);
            font-size: 16px;
            line-height: 1.5;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
            /* max-width: 375px; Removed */
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px; /* Ensure 8px */
            transition: var(--transition);
        }
        
        .back-button a:hover {
            /* background-color: rgba(0, 122, 255, 0.1); */
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Doodle Jump</h1>
            <p class="subtitle">Springe auf Plattformen und erreiche neue Höhen!</p>
        </header>

        <div class="score-container">
            <div class="score-box" id="score-display">Score: 0</div>
            <div class="score-box" id="high-score">Highscore: 0</div>
        </div>

        <div class="game-container">
            <canvas id="game" width="375" height="600"></canvas>
            
            <div id="start-screen">
                <h2 style="color: white; margin-bottom: 20px;">Doodle Jump</h2>
                <p style="color: white; margin-bottom: 20px;">Springe so hoch du kannst!</p>
                <button id="start-button" class="btn">Spielen</button>
            </div>
            
            <div id="game-over">
                <h2 style="color: white; margin-bottom: 20px;">Game Over</h2>
                <p id="final-score" style="color: white; margin-bottom: 20px;">Score: 0</p>
                <button id="restart-button" class="btn">Neu starten</button>
            </div>
        </div>

        <div class="controls">
            <h3>Steuerung</h3>
            <p>Verwende die <strong>Pfeiltasten ← →</strong> oder neige dein Gerät, um zu steuern.</p>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const canvas = document.getElementById('game');
            const ctx = canvas.getContext('2d');
            const startScreen = document.getElementById('start-screen');
            const gameOverScreen = document.getElementById('game-over');
            const startButton = document.getElementById('start-button');
            const restartButton = document.getElementById('restart-button');
            const scoreDisplay = document.getElementById('score-display');
            const finalScoreDisplay = document.getElementById('final-score');
            const highScoreDisplay = document.getElementById('high-score');

            // Game variables
            let gameRunning = false;
            let platforms = [];
            let score = 0;
            let highScore = localStorage.getItem('doodleJumpHighScore') || 0;
            let maxHeight = 0; // Speichert die maximale Höhe, die der Spieler erreicht hat
            let heightOffset = 0; // Offset für die Höhenberechnung
            const FRAGILE_PLATFORM_THRESHOLD = 500; // Score, ab dem zerbrechliche Plattformen erscheinen
            const MAX_JUMP_HEIGHT = 200; // Maximale Sprunghöhe des Doodles
            
            // Player
            const doodler = {
                x: canvas.width / 2 - 20,
                y: canvas.height - 100,
                width: 40,
                height: 40,
                speedY: 0,
                speedX: 0,
                gravity: 0.25,
                jumpForce: -10,
                isJumping: false
            };

            // Platform settings
            const platformWidth = 70;
            const platformHeight = 15;
            const platformCount = 8;

            // Update high score display
            highScoreDisplay.textContent = `Highscore: ${highScore}`;

            // Initialize game
            function init() {
                platforms = [];
                score = 0;
                maxHeight = 0;
                heightOffset = 0;
                doodler.y = canvas.height - 100;
                doodler.x = canvas.width / 2 - 20;
                doodler.speedY = 0;
                
                // Create initial platforms
                createPlatforms();
                
                // Reset score display
                scoreDisplay.textContent = `Score: 0`;
                
                // Hide start screen
                startScreen.style.display = 'none';
                gameOverScreen.style.display = 'none';
                
                // Start game loop
                gameRunning = true;
                requestAnimationFrame(update);
            }

            // Create platforms
            function createPlatforms() {
                // First platform directly under the player for easier start
                platforms.push({
                    x: canvas.width / 2 - platformWidth / 2,
                    y: canvas.height - 50,
                    width: platformWidth,
                    height: platformHeight,
                    type: 'normal' // Normal Plattform
                });
                
                // Create remaining platforms
                for (let i = 0; i < platformCount - 1; i++) {
                    platforms.push({
                        x: Math.random() * (canvas.width - platformWidth),
                        y: canvas.height - 100 - i * (canvas.height / platformCount),
                        width: platformWidth,
                        height: platformHeight,
                        type: 'normal' // Normal Plattform
                    });
                }
            }

            // Create new platform at the top
            function createPlatform() {
                // Determine platform type based on score
                let platformType = 'normal';
                
                // Ab einem bestimmten Score können zerbrechliche Plattformen erscheinen
                if (score >= FRAGILE_PLATFORM_THRESHOLD) {
                    // Je höher der Score, desto wahrscheinlicher werden zerbrechliche Plattformen
                    const fragileChance = Math.min(0.5, (score - FRAGILE_PLATFORM_THRESHOLD) / 500);
                    if (Math.random() < fragileChance) {
                        platformType = 'fragile';
                    }
                }
                
                platforms.push({
                    x: Math.random() * (canvas.width - platformWidth),
                    y: 0,
                    width: platformWidth,
                    height: platformHeight,
                    type: platformType,
                    health: platformType === 'fragile' ? 1 : Infinity // Zerbrechlichkeit
                });
            }

            // Prüft, ob es mindestens eine erreichbare Plattform gibt
            function hasReachablePlatform() {
                // Überprüfe nur Plattformen im oberen Bereich (neue Plattformen)
                const topPlatforms = platforms.filter(p => p.y < MAX_JUMP_HEIGHT);
                
                if (topPlatforms.length === 0) return false;
                
                // Überprüfe, ob mindestens eine Plattform horizontal erreichbar ist
                const HORIZONTAL_REACH = canvas.width / 2; // Doodle kann etwa die Hälfte der Breite erreichen
                
                // Wir verwenden die aktuelle Position des Doodles als Referenz
                const doodleCenter = doodler.x + doodler.width / 2;
                
                for (const platform of topPlatforms) {
                    const platformCenter = platform.x + platform.width / 2;
                    const horizontalDistance = Math.abs(platformCenter - doodleCenter);
                    
                    if (horizontalDistance < HORIZONTAL_REACH) {
                        return true;
                    }
                }
                
                return false;
            }

            // Erzeugt eine garantiert erreichbare Plattform
            function createReachablePlatform() {
                // Position in Reichweite des Doodles
                const doodleCenter = doodler.x + doodler.width / 2;
                const horizontalVariance = canvas.width / 4; // Etwas Variation für Zufall
                
                // Berechne x-Position in der Nähe des Doodles
                const x = Math.max(0, Math.min(
                    canvas.width - platformWidth,
                    doodleCenter - platformWidth/2 + (Math.random() * 2 - 1) * horizontalVariance
                ));
                
                platforms.push({
                    x: x,
                    y: 0,
                    width: platformWidth,
                    height: platformHeight,
                    type: 'normal', // Erreichbare Plattformen sind immer normal
                    health: Infinity
                });
            }

            // Draw everything
            function draw() {
                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw platforms
                platforms.forEach(platform => {
                    // Wähle Farbe je nach Plattformtyp
                    if (platform.type === 'normal') {
                        ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--platform-color').trim();
                    } else if (platform.type === 'fragile') {
                        ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--platform-fragile-color').trim();
                    } else if (platform.type === 'breaking') {
                        ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--platform-broken-color').trim();
                    }
                    
                    ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
                    
                    // Risse für zerbrechliche Plattformen zeichnen
                    if (platform.type === 'fragile') {
                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
                        ctx.lineWidth = 1;
                        
                        // Zufällige Risse
                        const crackCount = 3;
                        ctx.beginPath();
                        for (let i = 0; i < crackCount; i++) {
                            const startX = platform.x + platform.width * (0.3 + Math.random() * 0.4);
                            ctx.moveTo(startX, platform.y);
                            ctx.lineTo(startX + (Math.random() * 10 - 5), platform.y + platform.height);
                        }
                        ctx.stroke();
                    }
                    
                    // Animation für brechende Plattformen
                    if (platform.type === 'breaking') {
                        // Fallende Partikel
                        ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--platform-broken-color').trim();
                        for (let i = 0; i < 5; i++) {
                            const particleSize = Math.random() * 3 + 1;
                            const x = platform.x + Math.random() * platform.width;
                            const y = platform.y + platform.height + Math.random() * 10;
                            ctx.fillRect(x, y, particleSize, particleSize);
                        }
                    }
                });
                
                // Draw doodler
                ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--doodle-color').trim();
                ctx.fillRect(doodler.x, doodler.y, doodler.width, doodler.height);
                
                // Draw eyes
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(doodler.x + 10, doodler.y + 10, 5, 0, Math.PI * 2);
                ctx.arc(doodler.x + 30, doodler.y + 10, 5, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw pupils
                ctx.fillStyle = '#000000';
                ctx.beginPath();
                ctx.arc(doodler.x + 12, doodler.y + 10, 2, 0, Math.PI * 2);
                ctx.arc(doodler.x + 28, doodler.y + 10, 2, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw mouth
                ctx.beginPath();
                ctx.arc(doodler.x + 20, doodler.y + 25, 10, 0, Math.PI);
                ctx.stroke();
            }

            // Game update function
            function update() {
                if (!gameRunning) return;
                
                // Apply gravity
                doodler.speedY += doodler.gravity;
                doodler.y += doodler.speedY;
                doodler.x += doodler.speedX;
                
                // Horizontal screen wrapping
                if (doodler.x > canvas.width) {
                    doodler.x = 0;
                } else if (doodler.x + doodler.width < 0) {
                    doodler.x = canvas.width;
                }
                
                // Check if doodler is moving up
                if (doodler.speedY < 0) {
                    // Move screen down by moving platforms up
                    const screenShift = Math.abs(doodler.speedY);
                    
                    if (doodler.y < canvas.height / 2) {
                        // Update height offset as player moves upward
                        heightOffset += screenShift;
                        
                        platforms.forEach(platform => {
                            platform.y += screenShift;
                        });
                        
                        // Doodler should stay in the same position
                        doodler.y += screenShift;
                        
                        // Entferne Plattformen, die außerhalb des Bildschirms sind oder zerbrechen
                        platforms = platforms.filter(platform => {
                            return platform.y < canvas.height && platform.type !== 'breaking';
                        });
                        
                        // Stelle sicher, dass immer genug Plattformen vorhanden sind
                        while (platforms.length < platformCount) {
                            // Prüfe, ob wir eine garantiert erreichbare Plattform brauchen
                            if (platforms.length === 0 || !hasReachablePlatform()) {
                                createReachablePlatform();
                            } else {
                                createPlatform();
                            }
                        }
                    }
                }
                
                // Update score based on height
                const currentHeight = Math.floor(heightOffset / 10);
                if (currentHeight > maxHeight) {
                    maxHeight = currentHeight;
                    score = maxHeight;
                    scoreDisplay.textContent = `Score: ${score}`;
                }
                
                // Check for collision with platforms when falling
                if (doodler.speedY > 0) {
                    for (let i = 0; i < platforms.length; i++) {
                        const platform = platforms[i];
                        if (
                            doodler.y + doodler.height > platform.y &&
                            doodler.y + doodler.height < platform.y + platform.height &&
                            doodler.x + doodler.width > platform.x &&
                            doodler.x < platform.x + platform.width
                        ) {
                            // Springe nur, wenn die Plattform nicht bereits zerbricht
                            if (platform.type !== 'breaking') {
                                doodler.speedY = doodler.jumpForce;
                                doodler.isJumping = true;
                                
                                // Wenn es eine zerbrechliche Plattform ist, reduziere die Gesundheit
                                if (platform.type === 'fragile') {
                                    platform.health--;
                                    
                                    // Wenn keine Gesundheit mehr, beginne den Bruchprozess
                                    if (platform.health <= 0) {
                                        platform.type = 'breaking';
                                        
                                        // Nach kurzer Verzögerung verschwindet die Plattform
                                        setTimeout(() => {
                                            const index = platforms.indexOf(platform);
                                            if (index !== -1) {
                                                platforms.splice(index, 1);
                                            }
                                        }, 300);
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                
                // Game over if doodler falls below the screen
                if (doodler.y > canvas.height) {
                    gameOver();
                    return;
                }
                
                // Draw everything
                draw();
                
                // Continue game loop
                requestAnimationFrame(update);
            }

            // Game over function
            function gameOver() {
                gameRunning = false;
                
                // Check for high score
                if (score > highScore) {
                    highScore = score;
                    localStorage.setItem('doodleJumpHighScore', highScore);
                    highScoreDisplay.textContent = `Highscore: ${highScore}`;
                }
                
                // Show game over screen
                finalScoreDisplay.textContent = `Score: ${score}`;
                gameOverScreen.style.display = 'flex';
            }

            // Event listeners
            startButton.addEventListener('click', init);
            restartButton.addEventListener('click', init);
            
            // Keyboard controls
            document.addEventListener('keydown', e => {
                if (!gameRunning) return;
                
                if (e.key === 'ArrowLeft') {
                    doodler.speedX = -5;
                } else if (e.key === 'ArrowRight') {
                    doodler.speedX = 5;
                }
            });
            
            document.addEventListener('keyup', e => {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    doodler.speedX = 0;
                }
            });

            // Mobile controls using device orientation if available
            if (window.DeviceOrientationEvent) {
                // Für Safari/iOS - Berechtigungsabfrage erforderlich
                if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                    // Safari auf iOS 13+
                    const permissionButton = document.createElement('button');
                    permissionButton.innerText = 'Neigungssteuerung aktivieren';
                    permissionButton.className = 'btn';
                    permissionButton.style.position = 'absolute';
                    permissionButton.style.bottom = '20px';
                    permissionButton.style.left = '50%';
                    permissionButton.style.transform = 'translateX(-50%)';
                    permissionButton.style.zIndex = '20';
                    
                    permissionButton.addEventListener('click', () => {
                        DeviceOrientationEvent.requestPermission()
                            .then(permissionState => {
                                if (permissionState === 'granted') {
                                    // Benutzer hat Zugriff gewährt
                                    window.addEventListener('deviceorientation', handleOrientation);
                                    permissionButton.remove();
                                }
                            })
                            .catch(console.error);
                    });
                    
                    document.querySelector('.game-container').appendChild(permissionButton);
                } else {
                    // Andere Browser, die DeviceOrientation unterstützen
                    window.addEventListener('deviceorientation', handleOrientation);
                }
                
                // Funktion zur Behandlung der Orientierungsdaten
                function handleOrientation(e) {
                    if (!gameRunning) return;
                    
                    if (e.gamma === null && e.beta === null) return; // Keine gültigen Orientierungsdaten
                    
                    // Erkenne die Bildschirmausrichtung
                    const orientation = (window.orientation !== undefined) ? 
                        window.orientation : 
                        (screen.orientation ? screen.orientation.angle : 0);
                    
                    // Anpassbarer Empfindlichkeitsfaktor
                    const sensitivity = 3;
                    
                    // Unterschiedliche Achsen je nach Ausrichtung verwenden
                    if (orientation === 0 || orientation === 180) {
                        // Portrait-Modus: Verwende Gamma (seitliche Neigung)
                        doodler.speedX = Math.max(-7, Math.min(7, e.gamma / sensitivity));
                    } else if (orientation === 90 || orientation === -90) {
                        // Landscape-Modus: Verwende Beta (vor/zurück Neigung)
                        // Vorzeichen umkehren basierend auf der Ausrichtung
                        const multiplier = orientation === 90 ? 1 : -1;
                        doodler.speedX = Math.max(-7, Math.min(7, (e.beta * multiplier) / sensitivity));
                    }
                }
                
                // Überwache Änderungen der Bildschirmausrichtung
                window.addEventListener('orientationchange', () => {
                    // Kurze Verzögerung, um dem Browser Zeit für die Anpassung zu geben
                    setTimeout(() => {
                        // Setze die Geschwindigkeit zurück, wenn die Orientierung geändert wird
                        doodler.speedX = 0;
                    }, 200);
                });
            }
        });
    </script>
</body>
</html> 
