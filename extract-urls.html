<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Extract URLs">
    <meta property="og:description" content="Extrahiert alle URLs aus einem Text und listet sie übersichtlich auf.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/extract-urls">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Extract URLs</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --success-color: #34c759;
            --background-color: #F5F5F7;
            --surface-color: #FFFFFF;
            --text-color: #1D1D1F;
            --border-color: #E5E5E7;
            --secondary-text-color: #484848;
            --border-radius: 10px;
            --transition: all 0.2s ease;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --success-color: #30D158;
                --background-color: #1C1C1E;
                --surface-color: #2C2C2E;
                --text-color: #FFFFFF;
                --border-color: #3A3A3C;
                --secondary-text-color: #8E8E93;
            }
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.5;
        }

        h1 {
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            letter-spacing: -0.3px;
        }

        p {
            font-size: 17px;
            color: var(--secondary-text-color);
            margin-bottom: 24px;
        }

        #input {
            width: 100%;
            min-height: 120px;
            margin-bottom: 24px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 16px;
            background-color: var(--surface-color);
            color: var(--text-color);
            font-size: 16px;
            transition: border-color 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        #input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        #output-container {
            display: none;
        }

        #output {
            width: 100%;
            min-height: 150px;
            margin-bottom: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 16px;
            font-size: 16px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, monospace;
            resize: none;
            background-color: var(--surface-color);
            color: var(--text-color);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        #copy-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        #copy-button:hover {
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
            transform: translateY(-1px);
        }

        #copy-button.success {
            background-color: var(--success-color);
        }

        #copy-button.success:hover {
            background-color: color-mix(in srgb, var(--success-color) 85%, black);
        }

        #copy-button svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        #copy-button.success svg {
            animation: checkmark 0.2s ease-in-out;
        }

        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }

        @media (max-width: 600px) {
            body {
                padding: 24px 16px;
            }

            h1 {
                font-size: 28px;
            }

            #input, #output {
                min-height: 100px;
            }
        }
    </style>
</head>
<body>
    <h1>Extract URLs</h1>
    <p>Copy content from a web page and paste here to extract linked URLs:</p>
    <div id="input" contenteditable="true"></div>
    <div id="output-container">
        <h2>Extracted</h2>
        <textarea id="output" readonly></textarea>
        <button id="copy-button">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
            Copy to clipboard
        </button>
    </div>

    <div class="back-button">
      <a href="index.html">← Zurück zur Übersicht</a>
    </div>

    <script>
        const input = document.getElementById('input');
        const outputContainer = document.getElementById('output-container');
        const output = document.getElementById('output');
        const copyButton = document.getElementById('copy-button');

        input.addEventListener('paste', function(e) {
            e.preventDefault();
            
            const clipboardData = e.clipboardData || window.clipboardData;
            const pastedData = clipboardData.getData('text/html');

            const temp = document.createElement('div');
            temp.innerHTML = pastedData;

            const links = temp.getElementsByTagName('a');
            const urls = Array.from(links)
                .map(link => link.href)
                .filter(url => url.startsWith('http'));

            if (urls.length > 0) {
                output.value = urls.join('\n');
                outputContainer.style.display = 'block';
            } else {
                outputContainer.style.display = 'none';
            }

            input.textContent = 'Content pasted. URLs extracted.';
        });

        input.addEventListener('focus', function() {
            if (input.textContent === 'Content pasted. URLs extracted.') {
                input.textContent = '';
            }
        });

        copyButton.addEventListener('click', async function() {
            try {
                output.select();
                document.execCommand('copy');
                
                const originalText = copyButton.textContent;
                const originalIcon = copyButton.querySelector('svg').outerHTML;
                
                copyButton.classList.add('success');
                copyButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                    </svg>
                    Copied!
                `;
                
                setTimeout(() => {
                    copyButton.classList.remove('success');
                    copyButton.innerHTML = originalIcon + 'Copy to clipboard';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy text: ', err);
            }
        });
    </script>
</body>
</html>
