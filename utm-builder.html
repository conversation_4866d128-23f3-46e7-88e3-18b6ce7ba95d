<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="UTM Campaign URL Builder">
    <meta property="og:description" content="Erstelle UTM-Campaign URLs für Google Analytics, Facebook Ads und mehr.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/utm-builder">
    <meta property="og:image" content="/assets/og-image.png">
    <title>UTM Campaign URL Builder</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css"
          integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a"
          crossorigin="anonymous">
    <style>
        :root {
            --text-color: #333333;
            --background-color: #FFFFFF;
            --primary-color: #007AFF;
            --secondary-color: #F5F5F7;
            --border-color: #E5E5E5;
            --input-background: #FFFFFF;
            --border-radius: 8px;
            --transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
            --success-color: #34C759;
            --description-text-color: #999999;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #F5F5F7;
                --background-color: #1E1E1E;
                --primary-color: #0A84FF;
                --secondary-color: #2C2C2E;
                --border-color: #3A3A3C;
                --input-background: #2C2C2E;
                --success-color: #30D158;
                --description-text-color: #777777;
            }
        }

        body {
            color: var(--text-color);
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: var(--transition);
        }

        .container {
            max-width: 800px;
            width: 100%;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        p {
             margin-bottom: 20px;
             text-align: center;
        }

        main {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        label {
            margin-bottom: 8px;
            font-weight: 500;
        }

        input[type="text"],
        input[type="url"] {
            height: 40px;
            padding: 0 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 16px;
            background-color: var(--input-background);
            color: var(--text-color);
            transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        input[type="text"]:focus,
        input[type="url"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }

        .generated-url-container {
            background-color: var(--secondary-color);
            padding: 15px;
            border-radius: var(--border-radius);
            margin-top: 20px;
            border: 1px solid var(--border-color);
        }

        .generated-url-container h2 {
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 10px;
        }

        #generated-url {
            word-wrap: break-word;
            font-family: monospace;
            background-color: var(--background-color);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            min-height: 30px;
            color: var(--text-color);
        }

        .copy-button {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease-in-out;
        }

        .copy-button:hover {
            background-color: #005bb5;
        }

        @media (prefers-color-scheme: dark) {
            .copy-button:hover {
                background-color: #006adc;
            }
        }

        .copy-button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }

        .copy-button.copy-success {
            background-color: var(--success-color);
            color: white;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            .back-button a:hover {
                background-color: rgba(10, 132, 255, 0.15);
            }
        }

        .form-group .description {
            font-size: 0.85rem;
            color: var(--description-text-color);
            margin-top: -4px;
            margin-bottom: 8px;
            text-align: left;
        }

    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>UTM Campaign URL Builder</h1>
            <p>Füge UTM-Parameter zu URLs hinzu, um Kampagnen zu verfolgen.</p>
        </header>

        <main id="utm-form">
            <div class="form-group">
                <label for="website-url">Website URL *</label>
                <p class="description">Die Ziel-URL der Kampagne (z.B. https://www.example.com).</p>
                <input type="url" id="website-url" required placeholder="https://www.example.com">
            </div>
            <div class="form-group">
                <label for="campaign-id">Campaign ID</label>
                <p class="description"><code>utm_id</code>: Eine optionale ID, um die Kampagne über Systeme hinweg zu identifizieren.</p>
                <input type="text" id="campaign-id" placeholder="z.B. abc.123">
            </div>
            <div class="form-group">
                <label for="campaign-source">Campaign Source *</label>
                <p class="description"><code>utm_source</code>: Der Werbeträger oder die Plattform (z.B. google, newsletter, facebook).</p>
                <input type="text" id="campaign-source" required placeholder="z.B. google, newsletter">
            </div>
            <div class="form-group">
                <label for="campaign-medium">Campaign Medium *</label>
                <p class="description"><code>utm_medium</code>: Das Marketingmedium (z.B. cpc, email, social, link).</p>
                <input type="text" id="campaign-medium" required placeholder="z.B. cpc, email, link">
            </div>
            <div class="form-group">
                <label for="campaign-name">Campaign Name</label>
                <p class="description"><code>utm_campaign</code>: Der spezifische Name der Kampagne (z.B. fruehling_sale).</p>
                <input type="text" id="campaign-name" placeholder="z.B. fruehling_sale">
            </div>
            <div class="form-group">
                <label for="campaign-term">Campaign Term</label>
                <p class="description"><code>utm_term</code>: Optional, für bezahlte Suche zur Keyword-Identifizierung.</p>
                <input type="text" id="campaign-term" placeholder="Suchbegriffe (für bezahlte Suche)">
            </div>
            <div class="form-group">
                <label for="campaign-content">Campaign Content</label>
                <p class="description"><code>utm_content</code>: Optional, zur Unterscheidung von Anzeigen/Links (z.B. logolink, textlink).</p>
                <input type="text" id="campaign-content" placeholder="Anzeigeninhalt (für A/B Tests)">
            </div>
        </main>

        <div class="generated-url-container">
            <h2>Generierte Kampagnen-URL</h2>
            <div id="generated-url"></div>
            <button id="copy-button" class="copy-button" disabled>URL kopieren</button>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        const form = document.getElementById('utm-form');
        const inputs = form.querySelectorAll('input[type="url"], input[type="text"]');
        const generatedUrlOutput = document.getElementById('generated-url');
        const websiteUrlInput = document.getElementById('website-url');
        const campaignSourceInput = document.getElementById('campaign-source');
        const campaignMediumInput = document.getElementById('campaign-medium');
        const copyButton = document.getElementById('copy-button');

        const utmParams = {
            'campaign-id': 'utm_id',
            'campaign-source': 'utm_source',
            'campaign-medium': 'utm_medium',
            'campaign-name': 'utm_campaign',
            'campaign-term': 'utm_term',
            'campaign-content': 'utm_content'
        };

        function saveToLocalStorage() {
            const data = {};
            inputs.forEach(input => {
                data[input.id] = input.value;
            });
            localStorage.setItem('utmBuilderData', JSON.stringify(data));
        }

        function loadFromLocalStorage() {
            const storedData = localStorage.getItem('utmBuilderData');
            if (storedData) {
                try {
                    const data = JSON.parse(storedData);
                    inputs.forEach(input => {
                        if (data.hasOwnProperty(input.id)) {
                            input.value = data[input.id];
                        }
                    });
                } catch (e) {
                    console.error('Error parsing UTM builder data from localStorage:', e);
                    localStorage.removeItem('utmBuilderData');
                }
            }
        }

        function buildUtmUrl() {
            const baseUrl = websiteUrlInput.value.trim();
            if (!baseUrl || !campaignSourceInput.value.trim() || !campaignMediumInput.value.trim()) {
                generatedUrlOutput.textContent = '';
                copyButton.disabled = true;
                return;
            }

            let url;
            try {
                 url = new URL(baseUrl.includes('://') ? baseUrl : `https://${baseUrl}`);
            } catch (e) {
                 generatedUrlOutput.textContent = 'Ungültige URL';
                 copyButton.disabled = true;
                 return;
            }


            const params = new URLSearchParams(url.search);

            Object.entries(utmParams).forEach(([inputId, paramName]) => {
                const inputElement = document.getElementById(inputId);
                const value = inputElement.value.trim();
                if (value) {
                    params.set(paramName, encodeURIComponent(value));
                } else {
                    params.delete(paramName);
                }
            });

            const sortedParams = new URLSearchParams();
            Array.from(params.keys()).sort().forEach(key => {
                params.getAll(key).forEach(value => {
                    sortedParams.append(key, value);
                });
             });

            url.search = sortedParams.toString();
            generatedUrlOutput.textContent = url.toString();
            copyButton.disabled = false;
        }

        inputs.forEach(input => {
            input.addEventListener('blur', saveToLocalStorage);
            input.addEventListener('input', buildUtmUrl);
        });

        copyButton.addEventListener('click', () => {
            const urlToCopy = generatedUrlOutput.textContent;
            if (urlToCopy && urlToCopy !== 'Ungültige URL') {
                navigator.clipboard.writeText(urlToCopy).then(() => {
                    copyButton.innerHTML = '<i class="fa-solid fa-check"></i>';
                    copyButton.classList.add('copy-success');
                    copyButton.disabled = true;
                    setTimeout(() => {
                        copyButton.textContent = 'URL kopieren';
                        copyButton.classList.remove('copy-success');
                        copyButton.disabled = !generatedUrlOutput.textContent || generatedUrlOutput.textContent === 'Ungültige URL';
                    }, 1500);
                }).catch(err => {
                    console.error('Fehler beim Kopieren:', err);
                    try {
                        const textArea = document.createElement("textarea");
                        textArea.value = urlToCopy;
                        textArea.style.position = "fixed";
                        document.body.appendChild(textArea);
                        textArea.focus();
                         textArea.select();
                         document.execCommand('copy');
                         document.body.removeChild(textArea);
                         copyButton.textContent = 'Kopiert!';
                         copyButton.classList.add('copy-success');
                         copyButton.disabled = true;
                            setTimeout(() => {
                                copyButton.textContent = 'URL kopieren';
                                copyButton.classList.remove('copy-success');
                                copyButton.disabled = !generatedUrlOutput.textContent || generatedUrlOutput.textContent === 'Ungültige URL';
                            }, 1500);
                     } catch (execErr) {
                         console.error('Fallback-Kopieren fehlgeschlagen:', execErr);
                         alert('Kopieren fehlgeschlagen. Bitte manuell kopieren.');
                     }

                });
            }
        });

        loadFromLocalStorage();
        buildUtmUrl();
    </script>
</body>
</html>
