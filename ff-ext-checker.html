<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox Extension Checker</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            background-color: #f9f9f9;
            color: #333;
        }

        .container {
            max-width: 960px;
            margin: 2rem auto;
            background-color: #fff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
        }

        #drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        #drop-zone.dragover {
            background-color: #e0e0e0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th,
        td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            cursor: pointer;
        }

        th:hover {
            background-color: #e0e0e0;
        }

        th .sort-indicator {
            margin-left: 0.5rem;
            color: #999;
        }

        th.sort-asc .sort-indicator::after {
            content: ' ▲';
        }

        th.sort-desc .sort-indicator::after {
            content: ' ▼';
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        a {
            color: #0066cc;
        }

        .status-active {
            color: green;
        }

        .status-inactive {
            color: red;
        }
    </style>
</head>

<body>

    <div class="container">
        <h1>Firefox Extension Checker</h1>

    <div id="drop-zone">
        extensions.json hierher ziehen oder klicken zum Auswählen
    </div>
    <input type="file" id="file-input" accept=".json" style="display: none;">

    <div id="filter-container" style="display:none; margin-top: 1rem;">
        <label>
            <input type="checkbox" id="show-active-only">
            Nur aktive Add-Ons anzeigen
        </label>
    </div>

    <table id="extensions-table" style="display:none;">
        <thead>
            <tr>
                <th data-sort-key="name" class="sort-asc" data-sort="asc">Name<span class="sort-indicator"></span></th>
                <th data-sort-key="version">Version<span class="sort-indicator"></span></th>
                <th data-sort-key="manifest">Manifest<span class="sort-indicator"></span></th>
                <th data-sort-key="installDate">Installiert am<span class="sort-indicator"></span></th>
                <th data-sort-key="status">Status<span class="sort-indicator"></span></th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>

    <script>
            const dropZone = document.getElementById('drop-zone');
            const fileInput = document.getElementById('file-input');
            const table = document.getElementById('extensions-table');
            const tableBody = table.querySelector('tbody');
            const showActiveOnlyCheckbox = document.getElementById('show-active-only');
            const filterContainer = document.getElementById('filter-container');

            let currentAddons = [];
            let allUserAddons = [];

            dropZone.addEventListener('click', () => fileInput.click());
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            function handleFile(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        allUserAddons = data.addons.filter(addon => addon.location !== 'app-builtin' && addon.location !== 'app-builtin-addons');
                        displayExtensions(allUserAddons, 'name', true);
                        filterContainer.style.display = 'block';
                    } catch (error) {
                        alert('Error parsing the JSON file: ' + error.message);
                    }
                };
                reader.readAsText(file);
            }

            function displayExtensions(addons, sortColumn = 'name', sortAsc = true) {
                currentAddons = addons;

                if (showActiveOnlyCheckbox.checked) {
                    currentAddons = currentAddons.filter(addon => addon.active);
                }

                currentAddons.sort((a, b) => {
                    let valA, valB;

                    switch (sortColumn) {
                        case 'name':
                            valA = a.defaultLocale?.name || a.id;
                            valB = b.defaultLocale?.name || b.id;
                            break;
                        case 'version':
                            valA = a.version;
                            valB = b.version;
                            break;
                        case 'manifest':
                            valA = a.manifestVersion;
                            valB = b.manifestVersion;
                            break;
                        case 'installDate':
                            valA = a.installDate || 0;
                            valB = b.installDate || 0;
                            break;
                        case 'status':
                            valA = a.active;
                            valB = b.active;
                            break;
                    }

                    if (valA < valB) return sortAsc ? -1 : 1;
                    if (valA > valB) return sortAsc ? 1 : -1;
                    return 0;
                });

                tableBody.innerHTML = ''; // Clear previous results

                currentAddons.forEach(addon => {
                    const row = tableBody.insertRow();

                    const name = addon.defaultLocale?.name || addon.id;
                    const version = addon.version;
                    const active = addon.active;
                    const manifestVersion = addon.manifestVersion;

                    row.insertCell().textContent = name;
                    row.insertCell().textContent = version;
                    row.insertCell().textContent = manifestVersion;

                    const installDateCell = row.insertCell();
                    if (addon.installDate) {
                        const date = new Date(addon.installDate);
                        installDateCell.innerHTML = `<time datetime="${date.toISOString()}">${date.toLocaleString('de-DE')}</time>`;
                    } else {
                        installDateCell.textContent = 'N/A';
                    }

                    const statusCell = row.insertCell();
                    statusCell.textContent = active ? 'Aktiv' : 'Inaktiv';
                    statusCell.className = active ? 'status-active' : 'status-inactive';
                });

                table.style.display = 'table';
                filterContainer.style.display = 'block';
            }

            showActiveOnlyCheckbox.addEventListener('change', () => {
                displayExtensions(allUserAddons, document.querySelector('th.sort-asc, th.sort-desc')?.dataset.sortKey || 'name', document.querySelector('th.sort-asc') ? true : false);
            });

            document.querySelectorAll('th').forEach(headerCell => {
                headerCell.addEventListener('click', () => {
                    const sortKey = headerCell.dataset.sortKey;
                    const currentSort = headerCell.dataset.sort || 'asc';
                    const newSort = currentSort === 'asc' ? 'desc' : 'asc';

                    document.querySelectorAll('th').forEach(th => {
                        th.classList.remove('sort-asc', 'sort-desc');
                        th.dataset.sort = '';
                    });

                    headerCell.dataset.sort = newSort;
                    headerCell.classList.add(newSort === 'asc' ? 'sort-asc' : 'sort-desc');

                    displayExtensions(allUserAddons, sortKey, newSort === 'asc');
                });
            });
        </script>

    </div>
</body>

</html>
