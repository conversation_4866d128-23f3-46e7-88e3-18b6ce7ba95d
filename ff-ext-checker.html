<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox Extension Checker</title>
    <meta property="og:title" content="Firefox Extension Checker">
    <meta property="og:description" content="Analysiere deine Firefox-Erweiterungen aus der extensions.json Datei">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/ff-ext-checker">
    <meta property="og:image" content="/assets/og-image.png">
    <style>
        :root {
            --text-color: #333333;
            --background-color: #FFFFFF;
            --primary-color: #007AFF;
            --secondary-color: #F5F5F7;
            --border-color: #E5E5E5;
            --border-radius: 8px;
            --transition: all 0.2s ease;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #F5F5F7;
                --background-color: #1E1E1E;
                --primary-color: #0A84FF;
                --secondary-color: #2C2C2E;
                --border-color: #3A3A3C;
            }
        }

        body {
            color: var(--text-color);
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        header {
            text-align: center;
            margin-bottom: 32px;
        }

        h1 {
            font-size: 24px;
            margin: 0;
            color: var(--text-color);
        }

        main {
            background-color: var(--background-color);
        }

        #drop-zone {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 32px;
            text-align: center;
            cursor: pointer;
            margin-bottom: 24px;
            transition: var(--transition);
            background-color: var(--secondary-color);
        }

        #drop-zone:hover {
            border-color: var(--primary-color);
        }

        #drop-zone.dragover {
            background-color: rgba(0, 122, 255, 0.1);
            border-color: var(--primary-color);
        }

        #filter-container {
            margin: 24px 0;
        }

        #filter-container label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 24px;
            background-color: var(--background-color);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--secondary-color);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }

        th:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        th .sort-indicator {
            margin-left: 8px;
            color: var(--primary-color);
        }

        th.sort-asc .sort-indicator::after {
            content: ' ▲';
        }

        th.sort-desc .sort-indicator::after {
            content: ' ▼';
        }

        tr:nth-child(even) {
            background-color: var(--secondary-color);
        }

        tr:hover {
            background-color: rgba(0, 122, 255, 0.05);
        }

        .status-active {
            color: #34C759;
            font-weight: 500;
        }

        .status-inactive {
            color: #FF3B30;
            font-weight: 500;
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
            max-width: 375px;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            table {
                font-size: 14px;
            }

            th,
            td {
                padding: 8px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>Firefox Extension Checker</h1>
        </header>

        <main>
            <div id="drop-zone">
                extensions.json hierher ziehen oder klicken zum Auswählen
            </div>
            <input type="file" id="file-input" accept=".json" style="display: none;">

            <div id="filter-container" style="display:none;">
                <label>
                    <input type="checkbox" id="show-active-only">
                    Nur aktive Add-Ons anzeigen
                </label>
            </div>

            <table id="extensions-table" style="display:none;">
                <thead>
                    <tr>
                        <th data-sort-key="name" class="sort-asc" data-sort="asc">Name<span class="sort-indicator"></span></th>
                        <th data-sort-key="version">Version<span class="sort-indicator"></span></th>
                        <th data-sort-key="manifest">Manifest<span class="sort-indicator"></span></th>
                        <th data-sort-key="installDate">Installiert am<span class="sort-indicator"></span></th>
                        <th data-sort-key="status">Status<span class="sort-indicator"></span></th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>

            <div class="back-button">
                <a href="index.html">← Zurück zur Übersicht</a>
            </div>
        </main>

        <script>
            const dropZone = document.getElementById('drop-zone');
            const fileInput = document.getElementById('file-input');
            const table = document.getElementById('extensions-table');
            const tableBody = table.querySelector('tbody');
            const showActiveOnlyCheckbox = document.getElementById('show-active-only');
            const filterContainer = document.getElementById('filter-container');

            let currentAddons = [];
            let allUserAddons = [];

            const handleFile = (file) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        allUserAddons = data.addons.filter(addon => addon.location !== 'app-builtin' && addon.location !== 'app-builtin-addons');
                        displayExtensions(allUserAddons, 'name', true);
                        filterContainer.style.display = 'block';
                    } catch (error) {
                        alert('Fehler beim Parsen der JSON-Datei: ' + error.message);
                    }
                };
                reader.readAsText(file);
            };

            const displayExtensions = (addons, sortColumn = 'name', sortAsc = true) => {
                currentAddons = addons;

                if (showActiveOnlyCheckbox.checked) {
                    currentAddons = currentAddons.filter(addon => addon.active);
                }

                currentAddons.sort((a, b) => {
                    let valA, valB;

                    switch (sortColumn) {
                        case 'name':
                            valA = a.defaultLocale?.name || a.id;
                            valB = b.defaultLocale?.name || b.id;
                            break;
                        case 'version':
                            valA = a.version;
                            valB = b.version;
                            break;
                        case 'manifest':
                            valA = a.manifestVersion;
                            valB = b.manifestVersion;
                            break;
                        case 'installDate':
                            valA = a.installDate || 0;
                            valB = b.installDate || 0;
                            break;
                        case 'status':
                            valA = a.active;
                            valB = b.active;
                            break;
                    }

                    if (valA < valB) return sortAsc ? -1 : 1;
                    if (valA > valB) return sortAsc ? 1 : -1;
                    return 0;
                });

                tableBody.innerHTML = '';

                currentAddons.forEach(addon => {
                    const row = tableBody.insertRow();

                    const name = addon.defaultLocale?.name || addon.id;
                    const version = addon.version;
                    const active = addon.active;
                    const manifestVersion = addon.manifestVersion;

                    row.insertCell().textContent = name;
                    row.insertCell().textContent = version;
                    row.insertCell().textContent = manifestVersion;

                    const installDateCell = row.insertCell();
                    if (addon.installDate) {
                        const date = new Date(addon.installDate);
                        installDateCell.innerHTML = `<time datetime="${date.toISOString()}">${date.toLocaleString('de-DE')}</time>`;
                    } else {
                        installDateCell.textContent = 'N/A';
                    }

                    const statusCell = row.insertCell();
                    statusCell.textContent = active ? 'Aktiv' : 'Inaktiv';
                    statusCell.className = active ? 'status-active' : 'status-inactive';
                });

                table.style.display = 'table';
                filterContainer.style.display = 'block';
            };

            dropZone.addEventListener('click', () => fileInput.click());
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            showActiveOnlyCheckbox.addEventListener('change', () => {
                displayExtensions(allUserAddons, document.querySelector('th.sort-asc, th.sort-desc')?.dataset.sortKey || 'name', document.querySelector('th.sort-asc') ? true : false);
            });

            document.querySelectorAll('th').forEach(headerCell => {
                headerCell.addEventListener('click', () => {
                    const sortKey = headerCell.dataset.sortKey;
                    const currentSort = headerCell.dataset.sort || 'asc';
                    const newSort = currentSort === 'asc' ? 'desc' : 'asc';

                    document.querySelectorAll('th').forEach(th => {
                        th.classList.remove('sort-asc', 'sort-desc');
                        th.dataset.sort = '';
                    });

                    headerCell.dataset.sort = newSort;
                    headerCell.classList.add(newSort === 'asc' ? 'sort-asc' : 'sort-desc');

                    displayExtensions(allUserAddons, sortKey, newSort === 'asc');
                });
            });
        </script>
    </div>
</body>

</html>
