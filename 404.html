<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="404 - Seite nicht gefunden">
    <meta property="og:description" content="Die gesuchte Seite wurde nicht gefunden.">
    <meta property="og:type" content="website">
    <meta property="og:image" content="/assets/og-image.png">
    <title>404 - Seite nicht gefunden</title>
    <style>
        /* Grundlegende Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, 
                         Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        :root {
            --bg-color: #f5f5f7;
            --text-color: #1d1d1f;
            --container-bg: #ffffff;
            --container-shadow: rgba(0, 0, 0, 0.08);
            --secondary-text: #86868b;
            --button-bg: #007aff;
            --button-text: white;
            --gradient-start: #007aff;
            --gradient-end: #5856d6;
        }
        
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #1d1d1f;
                --text-color: #f5f5f7;
                --container-bg: #2c2c2e;
                --container-shadow: rgba(0, 0, 0, 0.3);
                --secondary-text: #aeaeb2;
                --button-bg: #0a84ff;
                --button-text: #ffffff;
                --gradient-start: #0a84ff;
                --gradient-end: #6e6adc;
            }
        }
        
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            padding: 40px;
            background-color: var(--container-bg);
            border-radius: 18px;
            box-shadow: 0 4px 20px var(--container-shadow);
        }
        
        h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .error-code {
            font-size: 100px;
            font-weight: 700;
            background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }
        
        p {
            font-size: 18px;
            line-height: 1.5;
            color: var(--secondary-text);
            margin-bottom: 25px;
        }
        
        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }
        
        .back-button a {
            display: inline-block;
            background-color: var(--button-bg);
            color: var(--button-text);
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            font-size: 17px;
            transition: all 0.2s ease;
        }
        
        .back-button a:hover {
            background-color: color-mix(in srgb, var(--button-bg) 85%, black);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 36px;
            }
            
            .error-code {
                font-size: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-code">404</div>
        <h1>Seite nicht gefunden</h1>
        <p>Die von dir gesuchte Seite existiert nicht oder wurde möglicherweise verschoben.</p>
        
        <div class="back-button">
          <a href="/">Zurück zur Startseite</a>
        </div>
    </div>
</body>
</html> 
