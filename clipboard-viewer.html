<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Clipboard Viewer">
    <meta property="og:description" content="Analysiert den Inhalt der Zwischenablage und zeigt die verschiedenen Datenformate an.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/clipboard-viewer">
    <meta property="og:image" content="/assets/og-image.png">
    <title>Clipboard Viewer</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css" 
          integrity="sha384-nRgPTkuX86pH8yjPJUAFuASXQSSl2/bBUiNV47vSYpKFxHJhbcrGnmlYpYJMeD7a" 
          crossorigin="anonymous">
    <style>
        :root {
            --text-color: #333333;
            --background-color: #FFFFFF;
            --primary-color: #007AFF;
            --secondary-color: #F5F5F7;
            --border-color: #E5E5E5;
            --border-radius: 8px;
            --transition: all 0.3s ease;
            --pre-bg-color: #f0f0f0;
            --pre-text-color: #333;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #F5F5F7;
                --background-color: #1E1E1E;
                --primary-color: #0A84FF;
                --secondary-color: #2C2C2E;
                --border-color: #3A3A3C;
                --pre-bg-color: #2c2c2e;
                --pre-text-color: #f5f5f7;
            }
        }

        * {
            box-sizing: border-box;
        }

        body {
            color: var(--text-color);
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 800px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h1 {
            color: var(--primary-color);
            font-size: 24px;
            margin-bottom: 16px;
            text-align: center;
        }

        p {
            margin-bottom: 16px;
            text-align: center;
            color: var(--text-color);
        }

        textarea {
            width: 100%;
            height: 150px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 16px;
            margin-bottom: 24px;
            background-color: var(--secondary-color);
            color: var(--text-color);
            resize: none;
        }

        #output {
            width: 100%;
        }

        .clipboard-item {
            background-color: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            overflow: hidden;
            position: relative;
        }

        .clipboard-item h3 {
            background-color: rgba(0, 122, 255, 0.1);
            padding: 10px 15px;
            margin: 0;
            font-size: 16px;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
        }

        .clipboard-item pre {
            padding: 15px;
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            background-color: var(--pre-bg-color);
            color: var(--pre-text-color);
        }

        .clipboard-item.image-container {
             padding-bottom: 10px;
        }

        .clipboard-item.image-container img {
            display: block;
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
            margin: 0 auto;
            overflow-y: auto;
        }

        .clipboard-item p.file-info {
            margin: 5px 15px;
            padding: 0;
            font-size: 14px;
            text-align: left;
        }

        .clipboard-item p.file-info strong {
            display: inline-block;
            width: 80px;
            color: var(--primary-color);
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .back-button a:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        #clipboardInfo {
            margin-top: 24px;
            padding: 15px;
            background-color: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            width: 100%;
            font-size: 14px;
        }

        #clipboardInfo p {
            margin-bottom: 8px;
            text-align: left;
        }

        #clipboardInfo strong {
            color: var(--primary-color);
            display: inline-block;
            width: 180px;
        }

        .copy-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: var(--primary-color);
            padding: 5px;
            line-height: 1;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .copy-button:hover {
            opacity: 1;
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Clipboard Viewer</h1>
        <p>Füge hier Inhalt aus deiner Zwischenablage ein, um die verfügbaren Datenformate anzuzeigen.</p>
        <textarea id="pasteArea" placeholder="Hier einfügen (Ctrl+V / Cmd+V)"></textarea>
        <div id="output"></div>
        <div id="clipboardInfo" style="display: none;"></div>
        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        const pasteArea = document.getElementById('pasteArea');
        const outputDiv = document.getElementById('output');
        const infoDiv = document.getElementById('clipboardInfo');

        pasteArea.addEventListener('paste', (event) => {
            event.preventDefault();
            const clipboardData = event.clipboardData || window.clipboardData;
            
            outputDiv.innerHTML = ''; 
            infoDiv.innerHTML = '';
            infoDiv.style.display = 'none';

            if (clipboardData) {
                const types = clipboardData.types;
                const items = clipboardData.items || [];
                const files = clipboardData.files || [];

                if (types && types.length > 0) {
                    types.forEach(type => {
                        if (type === 'Files') {
                            return;
                        }

                        const data = clipboardData.getData(type);

                        const itemDiv = document.createElement('div');
                        itemDiv.classList.add('clipboard-item');
                        
                        const typeHeader = document.createElement('h3');
                        typeHeader.textContent = `Typ: ${type}`;
                        itemDiv.appendChild(typeHeader);

                        const dataPre = document.createElement('pre');
                        const content = data || '(Inhalt nicht lesbar oder binär)';
                        dataPre.textContent = content;
                        itemDiv.appendChild(dataPre);

                        if (data) {
                            const copyButton = document.createElement('button');
                            copyButton.innerHTML = '<i class="fa-solid fa-copy"></i>';
                            copyButton.title = 'Inhalt kopieren';
                            copyButton.classList.add('copy-button');
                            copyButton.addEventListener('click', () => {
                                navigator.clipboard.writeText(data).then(() => {
                                    copyButton.innerHTML = '<i class="fa-solid fa-check"></i>';
                                    copyButton.classList.add('copied');
                                    setTimeout(() => {
                                        copyButton.innerHTML = '<i class="fa-solid fa-copy"></i>';
                                        copyButton.classList.remove('copied');
                                    }, 1500);
                                }).catch(err => {
                                    console.error('Fehler beim Kopieren: ', err);
                                    alert('Kopieren fehlgeschlagen.');
                                });
                            });
                            itemDiv.appendChild(copyButton);
                        }

                        outputDiv.appendChild(itemDiv);
                    });
                } else {
                    if (files.length === 0) {
                        outputDiv.textContent = 'Keine Formate in der Zwischenablage gefunden.';
                    }
                }

                if (files.length > 0) {
                    Array.from(files).forEach(file => {
                        const infoItemDiv = document.createElement('div');
                        infoItemDiv.classList.add('clipboard-item');
                        const infoTypeHeader = document.createElement('h3');
                        infoTypeHeader.textContent = 'Datei-Informationen';
                        infoItemDiv.appendChild(infoTypeHeader);

                        const nameP = document.createElement('p');
                        nameP.classList.add('file-info');
                        nameP.innerHTML = `<strong>Name:</strong> ${file.name}`;
                        infoItemDiv.appendChild(nameP);

                        const typeP = document.createElement('p');
                        typeP.classList.add('file-info');
                        typeP.innerHTML = `<strong>Typ:</strong> ${file.type || 'Unbekannt'}`;
                        infoItemDiv.appendChild(typeP);

                        const sizeP = document.createElement('p');
                        sizeP.classList.add('file-info');
                        sizeP.innerHTML = `<strong>Größe:</strong> ${formatBytes(file.size)}`;
                        infoItemDiv.appendChild(sizeP);
                        
                        outputDiv.appendChild(infoItemDiv);

                        if (file.type.startsWith('image/')) {
                            const imgItemDiv = document.createElement('div');
                            imgItemDiv.classList.add('clipboard-item', 'image-container');
                            
                            const imgTypeHeader = document.createElement('h3');
                            imgTypeHeader.textContent = 'Bildvorschau';
                            imgItemDiv.appendChild(imgTypeHeader);

                            const img = document.createElement('img');
                            img.alt = `Vorschau für ${file.name}`;
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                img.src = e.target.result;
                            };
                            reader.readAsDataURL(file);
                            imgItemDiv.appendChild(img);
                            outputDiv.appendChild(imgItemDiv);
                        }
                    });
                }

                let infoHTML = '<p><strong>Ereignistyp:</strong> paste</p>';
                infoHTML += `<p><strong>Verfügbare Formate (${types.length}):</strong> ${types.join(', ') || 'Keine'}</p>`;
                infoHTML += `<p><strong>Anzahl Dateien:</strong> ${files.length}</p>`;
                infoHTML += `<p><strong>Anzahl Clipboard Items:</strong> ${items.length}</p>`;

                infoDiv.innerHTML = infoHTML;
                infoDiv.style.display = 'block';

                 pasteArea.value = clipboardData.getData('text/plain') || pasteArea.value;
            } else {
                outputDiv.textContent = 'Konnte nicht auf die Zwischenablage zugreifen.';
                infoDiv.textContent = 'Konnte Clipboard-Informationen nicht abrufen.';
                infoDiv.style.display = 'block';
            }
        });

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1000;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            const value = bytes / Math.pow(k, i);

            if (i === 0) {
                return `${value.toFixed(0)} ${sizes[i]}`;
            } else {
                const valueRoundedToOne = parseFloat(value.toFixed(1));
                if (valueRoundedToOne % 1 === 0) {
                    return `${valueRoundedToOne.toFixed(0)} ${sizes[i]}`;
                } else {
                    return `${valueRoundedToOne.toFixed(1)} ${sizes[i]}`;
                }
            }
        }
    </script>
</body>
</html> 
