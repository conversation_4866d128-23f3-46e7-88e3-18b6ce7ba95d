<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta property="og:title" content="ICS Generator">
  <meta property="og:description" content="Erstellt Kalenderereignisse im iCalendar (ICS) Format zum Import in Kalender-Apps.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://tools.nyanya.de/ics-generator">
  <meta property="og:image" content="/assets/og-image.png">
  <title>ICS-Generator</title>
  <style>
    :root {
      --primary-color: #007AFF;
      --text-color: #1D1D1F;
      --background-color: #F5F5F7;
      --card-color: #FFFFFF;
      --secondary-color: #F5F5F7;
      --border-color: #E5E5E5;
      --error-color: #FF3B30;
      --success-color: #34C759;
      --border-radius: 12px;
      --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }
    
    @media (prefers-color-scheme: dark) {
      :root {
        --primary-color: #0A84FF;
        --text-color: #FFFFFF;
        --background-color: #1C1C1E;
        --card-color: #2C2C2E;
        --secondary-color: #2C2C2E;
        --border-color: #3A3A3C;
        --error-color: #FF453A;
        --success-color: #30D158;
        --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
      }
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background-color: var(--background-color);
      color: var(--text-color);
      line-height: 1.5;
      min-height: 100vh;
      transition: background-color 0.3s ease;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      text-align: center;
      padding: 20px 0;
      margin-bottom: 20px;
    }
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 12px;
    }
    
    .subtitle {
      font-size: 1.1rem;
      color: var(--secondary-text-color);
      margin-bottom: 20px;
    }
    
    .card {
      background-color: var(--card-color);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      padding: 24px;
      margin-bottom: 24px;
      transition: var(--transition);
    }
    
    .form-group {
      margin-bottom: 16px;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    input, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: 10px;
      font-size: 16px;
      box-sizing: border-box;
      background-color: var(--background-color);
      color: var(--text-color);
      transition: border-color 0.3s;
    }
    
    input[type="datetime-local"] {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    
    textarea {
      height: 100px;
      resize: vertical;
    }
    
    input:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
    }
    
    button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 10px;
      padding: 12px 20px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: opacity 0.3s;
    }
    
    button:hover {
      opacity: 0.9;
    }
    
    .button-group {
      display: flex;
      gap: 12px;
      margin: 20px 0;
    }
    
    @media (max-width: 768px) {
      .button-group {
        flex-direction: column;
      }
    }
    
    button.secondary {
      background-color: var(--secondary-color);
      color: var(--text-color);
      border: 1px solid var(--border-color);
    }
    
    .error {
      color: var(--error-color);
      font-size: 14px;
      margin-top: 4px;
    }
    
    .success {
      margin-top: 16px;
      padding: 12px;
      background-color: var(--success-color);
      color: white;
      border-radius: 10px;
      display: none;
    }
    
    .back-button {
      margin-top: 24px;
      text-align: center;
      width: 100%;
      max-width: 375px;
      margin-left: auto;
      margin-right: auto;
      display: flex;
      justify-content: center;
    }
    
    .back-button a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: var(--border-radius);
      transition: var(--transition);
    }
    
    .back-button a:hover {
      /* background-color: rgba(0, 122, 255, 0.1); */
      background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
    }
    
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>ICS-Generator</h1>
      <p class="subtitle">Erstelle Kalenderereignisse im iCalendar (ICS) Format zum Import in Kalender-Apps.</p>
    </header>
    
    <div class="card">
      <div class="form-group">
        <label for="title">Titel</label>
        <input type="text" id="title" placeholder="Meeting mit Team">
        <div id="title-error" class="error"></div>
      </div>
      
      <div class="form-group">
        <label for="location">Ort (optional)</label>
        <input type="text" id="location" placeholder="Konferenzraum 3">
      </div>
      
      <div class="form-group">
        <label for="description">Beschreibung (optional)</label>
        <textarea id="description" placeholder="Agenda: 1. Projekt-Update 2. Nächste Schritte"></textarea>
      </div>
      
      <div class="form-group">
        <label for="start">Startzeit</label>
        <input type="datetime-local" id="start">
        <div id="start-error" class="error"></div>
      </div>
      
      <div class="form-group">
        <label for="end">Endzeit</label>
        <input type="datetime-local" id="end">
        <div id="end-error" class="error"></div>
      </div>
      
      <div class="button-group">
        <button id="generate">ICS-Datei generieren</button>
        <button id="clear" class="secondary">Zurücksetzen</button>
      </div>
      
      <div id="success" class="success">
        ICS-Datei erfolgreich generiert. Der Download sollte automatisch starten.
      </div>
    </div>
    
    <div class="back-button">
      <a href="index.html">← Zurück zur Übersicht</a>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const generateBtn = document.getElementById('generate');
      const clearBtn = document.getElementById('clear');
      const titleInput = document.getElementById('title');
      const startInput = document.getElementById('start');
      const endInput = document.getElementById('end');
      const locationInput = document.getElementById('location');
      const descriptionInput = document.getElementById('description');
      const successMsg = document.getElementById('success');
      
      // Aktuelle Zeit vorausfüllen
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      
      // Format für datetime-local: YYYY-MM-DDThh:mm
      startInput.value = formatDateForInput(now);
      endInput.value = formatDateForInput(oneHourLater);
      
      generateBtn.addEventListener('click', generateICS);
      clearBtn.addEventListener('click', clearForm);
      
      function formatDateForInput(date) {
        return date.getFullYear() + '-' + 
               pad(date.getMonth() + 1) + '-' + 
               pad(date.getDate()) + 'T' + 
               pad(date.getHours()) + ':' + 
               pad(date.getMinutes());
      }
      
      function pad(num) {
        return num.toString().padStart(2, '0');
      }
      
      function validateForm() {
        let isValid = true;
        
        // Titel validieren
        if (!titleInput.value.trim()) {
          document.getElementById('title-error').textContent = 'Bitte gib einen Titel ein';
          isValid = false;
        } else {
          document.getElementById('title-error').textContent = '';
        }
        
        // Startzeit validieren
        if (!startInput.value) {
          document.getElementById('start-error').textContent = 'Bitte gib eine Startzeit ein';
          isValid = false;
        } else {
          document.getElementById('start-error').textContent = '';
        }
        
        // Endzeit validieren
        if (!endInput.value) {
          document.getElementById('end-error').textContent = 'Bitte gib eine Endzeit ein';
          isValid = false;
        } else if (new Date(endInput.value) <= new Date(startInput.value)) {
          document.getElementById('end-error').textContent = 'Die Endzeit muss nach der Startzeit liegen';
          isValid = false;
        } else {
          document.getElementById('end-error').textContent = '';
        }
        
        return isValid;
      }
      
      function formatDateForICS(dateString) {
        const date = new Date(dateString);
        return date.getUTCFullYear() + 
               pad(date.getUTCMonth() + 1) + 
               pad(date.getUTCDate()) + 'T' + 
               pad(date.getUTCHours()) + 
               pad(date.getUTCMinutes()) + 
               pad(date.getUTCSeconds()) + 'Z';
      }
      
      function escapeText(text) {
        return text
          .replace(/\n/g, '\\n')
          .replace(/,/g, '\\,')
          .replace(/;/g, '\\;');
      }
      
      function generateICS() {
        if (!validateForm()) return;
        
        const title = titleInput.value.trim();
        const location = locationInput.value.trim();
        const description = descriptionInput.value.trim();
        const start = formatDateForICS(startInput.value);
        const end = formatDateForICS(endInput.value);
        
        // Eindeutige ID erstellen
        const uid = Date.now() + "@ics-generator.local";
        
        // Aktuelle Zeit als Erstellungszeitstempel
        const now = formatDateForICS(new Date().toISOString());
        
        let icsContent = [
          'BEGIN:VCALENDAR',
          'VERSION:2.0',
          'PRODID:-//ICS-Generator//DE',
          'CALSCALE:GREGORIAN',
          'METHOD:PUBLISH',
          'BEGIN:VEVENT',
          `UID:${uid}`,
          `DTSTAMP:${now}`,
          `DTSTART:${start}`,
          `DTEND:${end}`,
          `SUMMARY:${escapeText(title)}`
        ];
        
        if (location) {
          icsContent.push(`LOCATION:${escapeText(location)}`);
        }
        
        if (description) {
          icsContent.push(`DESCRIPTION:${escapeText(description)}`);
        }
        
        icsContent = icsContent.concat([
          'END:VEVENT',
          'END:VCALENDAR'
        ]);
        
        // ICS-Datei erstellen und herunterladen
        const blob = new Blob([icsContent.join('\r\n')], { type: 'text/calendar' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.ics`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Erfolgsmeldung anzeigen
        successMsg.style.display = 'block';
        setTimeout(() => {
          successMsg.style.display = 'none';
        }, 5000);
      }
      
      function clearForm() {
        titleInput.value = '';
        locationInput.value = '';
        descriptionInput.value = '';
        
        // Aktuelle Zeit neu setzen
        const now = new Date();
        const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
        startInput.value = formatDateForInput(now);
        endInput.value = formatDateForInput(oneHourLater);
        
        // Fehler zurücksetzen
        document.getElementById('title-error').textContent = '';
        document.getElementById('start-error').textContent = '';
        document.getElementById('end-error').textContent = '';
        
        // Erfolgsmeldung verstecken
        successMsg.style.display = 'none';
      }
    });
  </script>
</body>
</html>
