Du programmierst "Tools"-Webseiten. Dies sind self-contained <PERSON>sei<PERSON>, die verschiedene Dinge tun. Schreibe alles (CSS, JavaScript) in eine einzige HTML-Datei. Benutze nur plain JavaScript. Kommentiere Code NIE. Schreibe KEINE Code-Kommentare.

Halte dich an Apples Human Interface Guidelines. Füge immer einen Dark-Mode hinzu, aber automatisch, ohne Button zum Umschalten. Füge neue Tools auch zur "index.html" hinzu.

## Gemeinsame Elemente für alle Tools-Webseiten

### Layout und Struktur
- Verwende eine saubere, minimalistische Benutzeroberfläche mit ausreichend Weißraum
- Strukturiere die Seite mit Header und Hauptinhalt
- Verwende ein responsives Design, das auf allen Geräten gut funktioniert
- Maximale Breite des Inhaltsbereichs: 1200px, mit auto margins für Zentrierung
- Kein Copyright im Footer
- Füge OpenGraph-Tags hinzu
- Verwende NIE Code-Kommentare
- Entferne Code-Kommentare

### Typografie
- Systemfonts: `-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif`
- Überschriften: Größen 24px (h1), 20px (h2), 18px (h3)
- Fließtext: 16px mit Zeilenhöhe 1.5
- Verwende semantische HTML-Elemente (h1, h2, p, etc.) statt div mit Klassen
- Du kannst FontAwesome benutzen, aber binde die lokale Version ein (s. unten)
- Schreibe in der Du-Form

### Farben
- Primärfarbe: #007AFF (Apple Blue)
- Text: #333333 (Dark Mode: #F5F5F7)
- Hintergrund: #FFFFFF (Dark Mode: #1E1E1E)
- Akzentfarben sparsam verwenden
- Subtile Hover-States für interaktive Elemente

### UI-Komponenten
- Buttons: abgerundete Ecken (8px), klare Hover-Zustände
- Input-Felder: konsistente Höhe (40px), leichte Umrandung
- Formularelemente: klare Labels oberhalb der Felder
- Tabellen: abwechselnde Zeilenfarben für bessere Lesbarkeit
- Listen: konsistenter Abstand zwischen Elementen

### Dark Mode
- Automatische Erkennung der Systemeinstellung mit `prefers-color-scheme`
- Helle Text- und UI-Elemente auf dunklem Hintergrund
- Stelle sicher, dass der Kontrast in beiden Modi ausreichend ist (WCAG AA standard)
- Teste beide Modi vor Fertigstellung

### JavaScript-Struktur
- Modularer Code mit klar benannten Funktionen
- Event-Listener am Ende des Dokuments hinzufügen
- Verwende ES6+ Features (const/let, arrow functions, etc.)
- Einfache Error-Handling-Mechanismen einbauen

### Performance
- Minimiere DOM-Manipulationen
- Vermeide unnötige Berechnungen in Loops
- Verwende effiziente Selektoren

### Barrierefreiheit
- Semantisches HTML für Screenreader
- Ausreichender Farbkontrast
- Tastaturnavigation ermöglichen
- Alt-Texte für Bilder

### Benutzerfreundlichkeit
- Klare Fehlermeldungen
- Feedback bei Benutzeraktionen
- Intuitive Navigation
- Konsistente Interaktionsmuster
- "Zurück zur Übersicht" Button im Footer

### Code für OpenGraph-Tags
```html
<meta property="og:title" content="TITEL DES TOOLS">
<meta property="og:description" content="KURZE BESCHREIBUNG DES TOOLS">
<meta property="og:type" content="website">
<meta property="og:url" content="https://tools.nyanya.de/LINK-OHNE-HTML">
<meta property="og:image" content="/assets/og-image.png">
```

### Code für FontAwesome
```html
<link rel="stylesheet" href="/assets/fontawesome/css/all.min.css">
```

### Code-Beispiel "Zurück zur Übersicht" Button
```css
.back-button {
    margin-top: 24px;
    text-align: center;
    width: 100%;
    max-width: 375px;
}

.back-button a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.back-button a:hover {
    background-color: rgba(0, 122, 255, 0.1);
}
```

```html
<div class="back-button">
    <a href="index.html">← Zurück zur Übersicht</a>
</div>
```

### Code-Beispiel für Dark Mode
```html
<style>
  :root {
    --text-color: #333333;
    --background-color: #FFFFFF;
    --primary-color: #007AFF;
    --secondary-color: #F5F5F7;
    --border-color: #E5E5E5;
  }
  
  @media (prefers-color-scheme: dark) {
    :root {
      --text-color: #F5F5F7;
      --background-color: #1E1E1E;
      --primary-color: #0A84FF;
      --secondary-color: #2C2C2E;
      --border-color: #3A3A3C;
    }
  }
  
  body {
    color: var(--text-color);
    background-color: var(--background-color);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
  }
</style>
```
