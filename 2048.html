<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="2048">
    <meta property="og:description" content="Spiele den Klassiker 2048 - Verbinde die Zahlen und versuche, die 2048-<PERSON><PERSON> zu erreichen!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/2048">
    <meta property="og:image" content="/assets/og-image.png">
    <title>2048</title>
    <style>
        :root {
            --primary-color: #007AFF;
            --background-color: #F5F5F7;
            --card-color: #FFFFFF;
            --text-color: #1D1D1F;
            --secondary-text-color: #6E6E73;
            --border-radius: 12px;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
            
            /* Game specific colors */
            --grid-bg: #BBADA0;
            --cell-empty: #CDC1B4;
            --cell-2: #EEE4DA;
            --cell-4: #EDE0C8;
            --cell-8: #F2B179;
            --cell-16: #F59563;
            --cell-32: #F67C5F;
            --cell-64: #F65E3B;
            --cell-128: #EDCF72;
            --cell-256: #EDCC61;
            --cell-512: #EDC850;
            --cell-1024: #EDC53F;
            --cell-2048: #EDC22E;
            --tile-color-light: #776E65;
            --tile-color-dark: #F9F6F2;
            --overlay-bg: rgba(238, 228, 218, 0.73);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #0A84FF;
                --background-color: #1C1C1E;
                --card-color: #2C2C2E;
                --text-color: #FFFFFF;
                --secondary-text-color: #8E8E93;
                --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                
                /* Game specific colors for dark mode - improved contrast */
                --grid-bg: #4A4A4E;
                --cell-empty: #323236;
                --cell-2: #5D5B58;
                --cell-4: #6D6855;
                --cell-8: #D79E63;
                --cell-16: #E68855;
                --cell-32: #E67255;
                --cell-64: #E65E3B;
                --cell-128: #EDCF72;
                --cell-256: #EDCC61;
                --cell-512: #EDC850;
                --cell-1024: #EDC53F;
                --cell-2048: #EDC22E;
                --tile-color-light: #FFFFFF; /* Improve text contrast on dark tiles */
                --tile-color-dark: #F9F6F2;
                --overlay-bg: rgba(45, 45, 47, 0.9);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .subtitle {
            font-size: 1.1rem;
            color: var(--secondary-text-color);
            margin-bottom: 20px;
        }

        .game-container {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 20px;
            margin-bottom: 20px;
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .score-container {
            display: flex;
            gap: 10px;
        }

        .score-box {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
            min-width: 100px;
        }

        .score-title {
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        .score-value {
            font-size: 1.2rem;
        }

        .game-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .game-button:hover {
            opacity: 0.9;
        }

        .grid-container {
            position: relative;
            background-color: var(--grid-bg);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            width: 100%;
            height: 0;
            padding-bottom: 100%; /* Makes it square */
        }

        .grid {
            position: absolute;
            top: 15px;
            right: 15px;
            bottom: 15px;
            left: 15px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, 1fr);
            grid-gap: 15px;
        }

        .grid-cell {
            position: relative;
            background-color: var(--cell-empty);
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }

        .tile {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 32px;
            transition: transform 0.1s, opacity 0.1s;
        }

        .tile-2 { background-color: var(--cell-2); color: var(--tile-color-light); }
        .tile-4 { background-color: var(--cell-4); color: var(--tile-color-light); }
        .tile-8 { background-color: var(--cell-8); color: var(--tile-color-dark); }
        .tile-16 { background-color: var(--cell-16); color: var(--tile-color-dark); }
        .tile-32 { background-color: var(--cell-32); color: var(--tile-color-dark); }
        .tile-64 { background-color: var(--cell-64); color: var(--tile-color-dark); }
        .tile-128 { background-color: var(--cell-128); color: var(--tile-color-dark); font-size: 28px; }
        .tile-256 { background-color: var(--cell-256); color: var(--tile-color-dark); font-size: 28px; }
        .tile-512 { background-color: var(--cell-512); color: var(--tile-color-dark); font-size: 28px; }
        .tile-1024 { background-color: var(--cell-1024); color: var(--tile-color-dark); font-size: 22px; }
        .tile-2048 { background-color: var(--cell-2048); color: var(--tile-color-dark); font-size: 22px; }

        /* Add dark mode specific tile colors with better contrast */
        @media (prefers-color-scheme: dark) {
            .tile-2, .tile-4 { 
                color: var(--tile-color-light);
                font-weight: 800; /* Bolder text for better readability */
            }
            
            .tile-8, .tile-16, .tile-32, .tile-64, .tile-128, .tile-256, .tile-512, .tile-1024, .tile-2048 {
                color: var(--tile-color-dark);
                text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2); /* Add subtle text shadow for better readability */
            }
        }

        .tile-new {
            animation: appear 0.2s;
        }

        @keyframes appear {
            0% { opacity: 0; transform: scale(0); }
            100% { opacity: 1; transform: scale(1); }
        }

        .tile-merged {
            animation: pop 0.2s;
        }

        @keyframes pop {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .instructions {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 20px;
            margin-bottom: 20px;
        }

        .instructions h2 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .instructions p {
            margin-bottom: 10px;
            font-size: 0.95rem;
            line-height: 1.5;
            color: var(--text-color);
        }

        .key-hint {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 8px;
        }

        .key {
            display: inline-block;
            background-color: var(--card-color);
            border: 1px solid var(--secondary-text-color);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 0.85rem;
            color: var(--text-color);
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }

        .touch-hint {
            text-align: center;
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--secondary-text-color);
        }

        .game-over {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--overlay-bg);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.5s;
        }

        .game-over.show {
            opacity: 1;
            pointer-events: auto;
        }

        .game-over-message {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .game-won {
            background-color: rgba(237, 194, 46, 0.5);
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
            max-width: 375px;
            margin-left: auto;
            margin-right: auto;
            display: flex;
            justify-content: center;
        }

        .back-button a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .back-button a:hover {
            background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
        }

        @media (max-width: 500px) {
            .grid {
                grid-gap: 10px;
            }

            .tile {
                font-size: 24px;
            }

            .tile-128, .tile-256, .tile-512 { font-size: 20px; }
            .tile-1024, .tile-2048 { font-size: 16px; }
        }

        /* Confirmation dialog styles */
        .confirm-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
        }

        .confirm-dialog.show {
            opacity: 1;
            pointer-events: auto;
        }

        .dialog-content {
            background-color: var(--card-color);
            padding: 25px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            max-width: 90%;
            width: 350px;
            text-align: center;
        }

        .dialog-message {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: var(--text-color);
        }

        .dialog-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .dialog-button {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }

        .confirm-button {
            background-color: var(--primary-color);
            color: white;
        }

        .cancel-button {
            background-color: #E1E1E6;
            color: #1D1D1F;
        }

        @media (prefers-color-scheme: dark) {
            .cancel-button {
                background-color: #3A3A3C;
                color: white;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>2048</h1>
            <p class="subtitle">Verbinde die Zahlen und versuche, die 2048-Kachel zu erreichen!</p>
        </header>

        <div class="game-container">
            <div class="game-header">
                <div class="score-container">
                    <div class="score-box">
                        <div class="score-title">SCORE</div>
                        <div class="score-value" id="score">0</div>
                    </div>
                    <div class="score-box">
                        <div class="score-title">BEST</div>
                        <div class="score-value" id="best-score">0</div>
                    </div>
                </div>
                <button id="new-game" class="game-button">Neu</button>
            </div>

            <div class="grid-container">
                <div class="grid" id="grid">
                    <!-- Grid cells will be created by JS -->
                </div>
                <div class="game-over" id="game-over">
                    <div class="game-over-message">Game Over!</div>
                    <button class="game-button" id="try-again">Nochmal</button>
                </div>
                <div class="game-over game-won" id="game-won">
                    <div class="game-over-message">Du hast gewonnen!</div>
                    <button class="game-button" id="keep-going">Weiterspielen</button>
                </div>
            </div>
        </div>

        <!-- Add confirmation dialog -->
        <div class="confirm-dialog" id="reset-confirm">
            <div class="dialog-content">
                <div class="dialog-message">Möchtest du wirklich neu anfangen? Dein aktueller Spielstand geht verloren.</div>
                <div class="dialog-buttons">
                    <button class="dialog-button cancel-button" id="reset-cancel">Abbrechen</button>
                    <button class="dialog-button confirm-button" id="reset-confirm-button">Neu starten</button>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h2>Wie man spielt</h2>
            <p>Benutze die Pfeiltasten, um alle Kacheln in eine Richtung zu bewegen. Wenn zwei Kacheln mit derselben Zahl sich berühren, werden sie zu einer!</p>
            <div class="key-hint">
                <span class="key">↑</span>
                <span class="key">←</span>
                <span class="key">↓</span>
                <span class="key">→</span>
            </div>
            <p class="touch-hint">Auf Mobilgeräten kannst du wischen, um die Kacheln zu bewegen.</p>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const grid = document.getElementById('grid');
            const scoreDisplay = document.getElementById('score');
            const bestScoreDisplay = document.getElementById('best-score');
            const newGameButton = document.getElementById('new-game');
            const gameOverMessage = document.getElementById('game-over');
            const gameWonMessage = document.getElementById('game-won');
            const tryAgainButton = document.getElementById('try-again');
            const keepGoingButton = document.getElementById('keep-going');
            
            let board = [];
            let score = 0;
            let bestScore = localStorage.getItem('2048-best-score') || 0;
            let gameOver = false;
            let gameWon = false;
            let keepPlaying = false;
            
            // Touch handling variables
            let touchStartX = 0;
            let touchStartY = 0;
            let touchEndX = 0;
            let touchEndY = 0;
            
            // Load best score from local storage
            bestScoreDisplay.textContent = bestScore;
            
            // Initialize the grid
            function initializeGrid() {
                grid.innerHTML = '';
                for (let i = 0; i < 16; i++) {
                    const cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.id = `cell-${i}`;
                    grid.appendChild(cell);
                }
            }
            
            // Initialize the game board
            function initializeBoard() {
                board = Array(16).fill(0);
                score = 0;
                gameOver = false;
                gameWon = false;
                keepPlaying = false;
                scoreDisplay.textContent = '0';
                gameOverMessage.classList.remove('show');
                gameWonMessage.classList.remove('show');
                
                // Add two initial tiles
                addRandomTile();
                addRandomTile();
                updateBoard();
            }
            
            // Add a random tile (2 or 4) to an empty cell
            function addRandomTile() {
                const emptyCells = board.reduce((acc, val, idx) => {
                    if (val === 0) acc.push(idx);
                    return acc;
                }, []);
                
                if (emptyCells.length > 0) {
                    const randomIndex = emptyCells[Math.floor(Math.random() * emptyCells.length)];
                    // 90% chance for a 2, 10% chance for a 4
                    board[randomIndex] = Math.random() < 0.9 ? 2 : 4;
                    return true;
                }
                return false;
            }
            
            // Update the visual board
            function updateBoard() {
                for (let i = 0; i < 16; i++) {
                    const cell = document.getElementById(`cell-${i}`);
                    
                    // Remove any existing tiles
                    while (cell.firstChild) {
                        cell.removeChild(cell.firstChild);
                    }
                    
                    // Add tile if the cell is not empty
                    if (board[i] !== 0) {
                        const tile = document.createElement('div');
                        tile.className = `tile tile-${board[i]}`;
                        tile.textContent = board[i];
                        
                        // Add animation class if it's a new tile
                        if (cell.dataset.newTile === 'true') {
                            tile.classList.add('tile-new');
                            cell.dataset.newTile = 'false';
                        }
                        
                        // Add animation class if it's a merged tile
                        if (cell.dataset.mergedTile === 'true') {
                            tile.classList.add('tile-merged');
                            cell.dataset.mergedTile = 'false';
                        }
                        
                        cell.appendChild(tile);
                    }
                }
            }
            
            // Update the score
            function updateScore(points) {
                score += points;
                scoreDisplay.textContent = score;
                
                if (score > bestScore) {
                    bestScore = score;
                    bestScoreDisplay.textContent = bestScore;
                    localStorage.setItem('2048-best-score', bestScore);
                }
            }
            
            // Check if the game is over (no valid moves)
            function checkGameOver() {
                // Check if the board is full
                if (board.includes(0)) return false;
                
                // Check if there are any possible merges
                for (let i = 0; i < 4; i++) {
                    for (let j = 0; j < 4; j++) {
                        const idx = i * 4 + j;
                        
                        // Check the tile to the right
                        if (j < 3 && board[idx] === board[idx + 1]) return false;
                        
                        // Check the tile below
                        if (i < 3 && board[idx] === board[idx + 4]) return false;
                    }
                }
                
                return true;
            }
            
            // Check if the game is won (2048 tile exists)
            function checkGameWon() {
                return board.includes(2048) && !keepPlaying;
            }
            
            // Move tiles in a direction (0=up, 1=right, 2=down, 3=left)
            function moveTiles(direction) {
                if (gameOver || (gameWon && !keepPlaying)) return false;
                
                let moved = false;
                let mergedCells = [];
                
                // Transform the 1D array into a 2D grid for easier manipulation
                const grid2D = [];
                for (let i = 0; i < 4; i++) {
                    grid2D.push(board.slice(i * 4, (i + 1) * 4));
                }
                
                // Function to process a row or column
                function processLine(line) {
                    const originalLine = [...line];
                    
                    // Remove zeros
                    const nonZeroTiles = line.filter(tile => tile !== 0);
                    
                    // Merge tiles
                    const mergedTiles = [];
                    for (let i = 0; i < nonZeroTiles.length; i++) {
                        if (i < nonZeroTiles.length - 1 && nonZeroTiles[i] === nonZeroTiles[i + 1]) {
                            mergedTiles.push(nonZeroTiles[i] * 2);
                            updateScore(nonZeroTiles[i] * 2);
                            i++; // Skip the next tile since it's been merged
                        } else {
                            mergedTiles.push(nonZeroTiles[i]);
                        }
                    }
                    
                    // Fill with zeros to maintain length
                    while (mergedTiles.length < 4) {
                        mergedTiles.push(0);
                    }
                    
                    // Check if the line has changed
                    const hasChanged = !originalLine.every((val, idx) => val === mergedTiles[idx]);
                    return { line: mergedTiles, changed: hasChanged };
                }
                
                // Process each row/column based on direction
                if (direction === 0) { // Up
                    for (let col = 0; col < 4; col++) {
                        let column = [grid2D[0][col], grid2D[1][col], grid2D[2][col], grid2D[3][col]];
                        const result = processLine(column);
                        
                        if (result.changed) {
                            moved = true;
                            for (let row = 0; row < 4; row++) {
                                const oldValue = grid2D[row][col];
                                const newValue = result.line[row];
                                grid2D[row][col] = newValue;
                                
                                const idx = row * 4 + col;
                                if (oldValue === 0 && newValue !== 0) {
                                    // Mark as new tile for animation
                                    document.getElementById(`cell-${idx}`).dataset.newTile = 'true';
                                } else if (oldValue !== 0 && oldValue !== newValue && newValue !== 0) {
                                    // Mark as merged tile for animation
                                    document.getElementById(`cell-${idx}`).dataset.mergedTile = 'true';
                                }
                            }
                        }
                    }
                } else if (direction === 1) { // Right
                    for (let row = 0; row < 4; row++) {
                        const reversedRow = [...grid2D[row]].reverse();
                        const result = processLine(reversedRow);
                        
                        if (result.changed) {
                            moved = true;
                            const processedRow = result.line.reverse();
                            for (let col = 0; col < 4; col++) {
                                const oldValue = grid2D[row][col];
                                const newValue = processedRow[col];
                                grid2D[row][col] = newValue;
                                
                                const idx = row * 4 + col;
                                if (oldValue === 0 && newValue !== 0) {
                                    document.getElementById(`cell-${idx}`).dataset.newTile = 'true';
                                } else if (oldValue !== 0 && oldValue !== newValue && newValue !== 0) {
                                    document.getElementById(`cell-${idx}`).dataset.mergedTile = 'true';
                                }
                            }
                        }
                    }
                } else if (direction === 2) { // Down
                    for (let col = 0; col < 4; col++) {
                        let column = [grid2D[0][col], grid2D[1][col], grid2D[2][col], grid2D[3][col]].reverse();
                        const result = processLine(column);
                        
                        if (result.changed) {
                            moved = true;
                            const processedColumn = result.line.reverse();
                            for (let row = 0; row < 4; row++) {
                                const oldValue = grid2D[row][col];
                                const newValue = processedColumn[row];
                                grid2D[row][col] = newValue;
                                
                                const idx = row * 4 + col;
                                if (oldValue === 0 && newValue !== 0) {
                                    document.getElementById(`cell-${idx}`).dataset.newTile = 'true';
                                } else if (oldValue !== 0 && oldValue !== newValue && newValue !== 0) {
                                    document.getElementById(`cell-${idx}`).dataset.mergedTile = 'true';
                                }
                            }
                        }
                    }
                } else if (direction === 3) { // Left
                    for (let row = 0; row < 4; row++) {
                        const result = processLine([...grid2D[row]]);
                        
                        if (result.changed) {
                            moved = true;
                            for (let col = 0; col < 4; col++) {
                                const oldValue = grid2D[row][col];
                                const newValue = result.line[col];
                                grid2D[row][col] = newValue;
                                
                                const idx = row * 4 + col;
                                if (oldValue === 0 && newValue !== 0) {
                                    document.getElementById(`cell-${idx}`).dataset.newTile = 'true';
                                } else if (oldValue !== 0 && oldValue !== newValue && newValue !== 0) {
                                    document.getElementById(`cell-${idx}`).dataset.mergedTile = 'true';
                                }
                            }
                        }
                    }
                }
                
                // Convert back to 1D array
                let newBoard = [];
                for (let i = 0; i < 4; i++) {
                    newBoard = newBoard.concat(grid2D[i]);
                }
                board = newBoard;
                
                if (moved) {
                    addRandomTile();
                    updateBoard();
                    
                    // Check if game is won
                    if (checkGameWon()) {
                        gameWon = true;
                        gameWonMessage.classList.add('show');
                    }
                    
                    // Check if game is over
                    if (checkGameOver()) {
                        gameOver = true;
                        gameOverMessage.classList.add('show');
                    }
                }
                
                return moved;
            }
            
            // Event listeners
            function handleKeyDown(e) {
                let moved = false;
                
                switch (e.key) {
                    case 'ArrowUp':
                        moved = moveTiles(0);
                        e.preventDefault();
                        break;
                    case 'ArrowRight':
                        moved = moveTiles(1);
                        e.preventDefault();
                        break;
                    case 'ArrowDown':
                        moved = moveTiles(2);
                        e.preventDefault();
                        break;
                    case 'ArrowLeft':
                        moved = moveTiles(3);
                        e.preventDefault();
                        break;
                }
            }
            
            function handleTouchStart(e) {
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
                e.preventDefault();
            }
            
            function handleTouchMove(e) {
                e.preventDefault(); // Prevent scrolling while swiping
            }
            
            function handleTouchEnd(e) {
                touchEndX = e.changedTouches[0].clientX;
                touchEndY = e.changedTouches[0].clientY;
                
                const diffX = touchEndX - touchStartX;
                const diffY = touchEndY - touchStartY;
                
                if (Math.abs(diffX) > Math.abs(diffY)) {
                    // Horizontal swipe
                    if (diffX > 20) {
                        moveTiles(1); // Left to right = Move right
                    } else if (diffX < -20) {
                        moveTiles(3); // Right to left = Move left
                    }
                } else {
                    // Vertical swipe
                    if (diffY > 20) {
                        moveTiles(2); // Top to bottom = Move down
                    } else if (diffY < -20) {
                        moveTiles(0); // Bottom to top = Move up
                    }
                }
                
                e.preventDefault();
            }
            
            // Initialize the game
            initializeGrid();
            initializeBoard();
            
            // Event listeners
            document.addEventListener('keydown', handleKeyDown);
            grid.addEventListener('touchstart', handleTouchStart, { passive: false });
            grid.addEventListener('touchmove', handleTouchMove, { passive: false });
            grid.addEventListener('touchend', handleTouchEnd, { passive: false });
            
            // Reset confirmation dialog
            const resetConfirmDialog = document.getElementById('reset-confirm');
            const resetConfirmButton = document.getElementById('reset-confirm-button');
            const resetCancelButton = document.getElementById('reset-cancel');
            
            // New game button click handler
            newGameButton.addEventListener('click', function() {
                // Only show confirmation if game is in progress (score > 0)
                if (score > 0) {
                    resetConfirmDialog.classList.add('show');
                } else {
                    initializeBoard();
                }
            });
            
            // Confirm reset button
            resetConfirmButton.addEventListener('click', function() {
                resetConfirmDialog.classList.remove('show');
                initializeBoard();
            });
            
            // Cancel reset button
            resetCancelButton.addEventListener('click', function() {
                resetConfirmDialog.classList.remove('show');
            });
            
            tryAgainButton.addEventListener('click', function() {
                initializeBoard();
            });
            
            keepGoingButton.addEventListener('click', function() {
                keepPlaying = true;
                gameWonMessage.classList.remove('show');
            });
        });
    </script>
</body>
</html> 
