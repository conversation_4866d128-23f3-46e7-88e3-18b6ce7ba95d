<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="XML Formatter">
    <meta property="og:description" content="Formatiert, validiert und verschönert XML-Dokumente mit Syntax-Highlighting.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tools.nyanya.de/xml-formatter">
    <meta property="og:image" content="/assets/og-image.png">
    <title>XML Formatter</title>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js" integrity="sha384-F/bZzf7p3Joyp5psL90p/p89AZJsndkSoGwRpXcZhleCWhd8SnRuoYo4d0yirjJp" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/xml.min.js" integrity="sha384-jgkY4GMNWfQcLLIoP1vg3FWXflDrRhcSXGBW6ONIWC2SOIv5H1Pa57sXs+aomCuZ" crossorigin="anonymous"></script>
    <style>
        :root {
            --system-gray: #8E8E93;
            --system-background: #FFFFFF;
            --system-secondary-background: #F2F2F7;
            --system-blue: #007AFF;
            --system-red: #FF3B30;
            --system-green: #34C759;
            --text-primary: #000000;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --system-gray: #8E8E93;
                --system-background: #000000;
                --system-secondary-background: #1C1C1E;
                --system-blue: #0A84FF;
                --system-red: #FF453A;
                --system-green: #30D158;
                --text-primary: #FFFFFF;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        body {
            background-color: var(--system-background);
            color: var(--system-gray);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        h1 {
            font-size: 34px;
            font-weight: 700;
            color: var(--system-blue);
            margin: 0;
        }

        .editor-container {
            display: flex;
            gap: 20px;
            flex-grow: 1;
        }

        .input-section, .output-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        textarea {
            flex-grow: 1;
            padding: 15px;
            border-radius: 10px;
            border: none;
            background-color: var(--system-secondary-background);
            color: var(--text-primary);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            resize: none;
            outline: none;
            overflow: hidden;
            min-height: 400px;
        }

        textarea::placeholder {
            color: var(--system-gray);
            opacity: 0.8;
        }

        .button-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        button {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            background-color: var(--system-blue);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: opacity 0.2s;
        }

        button:hover {
            /* opacity: 0.9; */
            background-color: color-mix(in srgb, var(--system-blue) 85%, black);
        }

        .upload-button {
            position: relative;
            overflow: hidden;
        }

        .upload-button input[type="file"] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            opacity: 0;
            outline: none;
            cursor: pointer;
        }

        .output-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            position: relative;
        }

        .copy-button {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 8px;
            border-radius: 6px;
            background-color: var(--system-blue);
            border: none;
            cursor: pointer;
            opacity: 0.9;
            transition: opacity 0.2s, background-color 0.2s;
            z-index: 20;
            display: flex;
            align-items: center;
            color: white;
        }

        .copy-button:hover {
            opacity: 1; /* Keep opacity change? Or just rely on color mix? */
            /* transform: scale(1.05); Removed */
             background-color: color-mix(in srgb, var(--system-blue) 85%, black);
        }

        .copy-button.success {
            background-color: var(--system-green);
        }

        .copy-button svg {
            width: 18px;
            height: 18px;
            fill: currentColor;
        }

        .status {
            position: absolute;
            top: 55px;
            right: 15px;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            max-width: calc(100% - 30px);
            word-wrap: break-word;
            white-space: pre-wrap;
            text-align: right;
        }

        .status.visible {
            opacity: 1;
        }

        .status.valid {
            background-color: var(--system-green);
            color: white;
        }

        .status.invalid {
            background-color: var(--system-red);
            color: white;
        }

        .output-code {
            flex-grow: 1;
            padding: 15px;
            border-radius: 10px;
            border: none;
            background-color: var(--system-secondary-background) !important;
            color: var(--text-primary);
            font-family: 'SF Mono', Monaco, monospace !important;
            font-size: 14px;
            overflow-y: auto;
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            scrollbar-width: thin;
            scrollbar-color: var(--system-gray) transparent;
        }

        .output-code code {
            background: none !important;
            font-family: inherit !important;
            font-size: inherit !important;
            color: var(--text-primary);
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            word-break: break-all !important;
        }

        .output-code .hljs {
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            word-break: break-all !important;
        }

        .output-code::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .output-code::-webkit-scrollbar-track {
            background: transparent;
        }

        .output-code::-webkit-scrollbar-thumb {
            background-color: var(--system-gray);
            border-radius: 4px;
            border: 2px solid var(--system-secondary-background);
        }

        .output-code::-webkit-scrollbar-thumb:hover {
            background-color: var(--system-blue);
        }

        /* Syntax Highlighting Styles */
        .hljs-tag {
            color: #0550AE;
            font-weight: 500;
        }
        .hljs-name {
            color: #0550AE;
            font-weight: 600;
        }
        .hljs-attr {
            color: #8250DF;
        }
        .hljs-string {
            color: #1F7B16;
        }

        /* Dark Mode Syntax Highlighting */
        @media (prefers-color-scheme: dark) {
            .hljs-tag {
                color: #75BAFF;
            }
            .hljs-name {
                color: #75BAFF;
            }
            .hljs-attr {
                color: #B392F0;
            }
            .hljs-string {
                color: #7EE787;
            }
        }

        @media (max-width: 768px) {
            .editor-container {
                flex-direction: column;
            }

            .header {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        .back-button {
            margin-top: 24px;
            text-align: center;
            width: 100%;
        }

        .back-button a {
            color: var(--system-blue); /* Use --system-blue as primary */
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px; /* Use consistent 8px radius */
            transition: background-color 0.2s; /* Use standard transition */
        }

        .back-button a:hover {
            background-color: color-mix(in srgb, var(--system-blue) 10%, transparent);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>XML Formatter & Validator</h1>
            <div class="button-group">
                <button onclick="validateAndFormatXML()">Formatieren</button>
                <button class="upload-button">
                    Datei öffnen
                    <input type="file" accept=".xml,application/xml,text/xml" onchange="handleFileUpload(event)">
                </button>
                <button onclick="clearInput()">Löschen</button>
            </div>
        </div>
        <div class="editor-container">
            <div class="input-section">
                <textarea id="input" placeholder="Füge hier deinen XML-Code ein..."></textarea>
            </div>
            <div class="output-section">
                <button onclick="copyToClipboard()" class="copy-button" id="copyButton">
                    <svg viewBox="0 0 24 24">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                </button>
                <div id="status" class="status"></div>
                <pre class="output-code"><code id="output" class="language-xml"></code></pre>
            </div>
        </div>

        <div class="back-button">
            <a href="index.html">← Zurück zur Übersicht</a>
        </div>
    </div>

    <script>
        const inputArea = document.getElementById('input');
        
        // Auto-grow Textarea
        function autoGrow() {
            inputArea.style.height = 'auto';
            inputArea.style.height = (inputArea.scrollHeight) + 'px';
        }
        
        inputArea.addEventListener('input', autoGrow);
        inputArea.addEventListener('paste', () => {
            setTimeout(() => {
                autoGrow();
                validateAndFormatXML();
            }, 0);
        });

        function formatXML(xml) {
            const serializer = new XMLSerializer();
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xml, 'text/xml');
            
            // Prüfen auf Parse-Fehler
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                throw new Error('XML Parse Error: ' + parseError[0].textContent);
            }

            // XSLT für die Formatierung
            const xslt = `
                <xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
                    <xsl:output method="xml" indent="yes"/>
                    <xsl:strip-space elements="*"/>
                    <xsl:template match="@*|node()">
                        <xsl:copy>
                            <xsl:apply-templates select="@*|node()"/>
                        </xsl:copy>
                    </xsl:template>
                </xsl:stylesheet>
            `;

            // XML als String zurückgeben
            return serializer.serializeToString(xmlDoc)
                .replace(/></g, '>\n<') // Grundlegende Formatierung
                .replace(/(<[^/].*?>)/g, '$1\n') // Neue Zeile nach öffnenden Tags
                .replace(/(<\/.*?>)/g, '\n$1') // Neue Zeile vor schließenden Tags
                .split('\n')
                .filter(line => line.trim())
                .map((line, index, array) => {
                    let indent = 0;
                    for (let i = 0; i < index; i++) {
                        if (array[i].match(/<[^/]/)) indent++;
                        if (array[i].match(/<\//)) indent--;
                    }
                    return '  '.repeat(Math.max(0, indent)) + line;
                })
                .join('\n');
        }

        function validateAndFormatXML() {
            const input = document.getElementById('input').value;
            const output = document.getElementById('output');
            const status = document.getElementById('status');

            try {
                if (!input.trim()) {
                    throw new Error('Bitte gib einen XML-Code ein.');
                }
                
                const formatted = formatXML(input);
                
                output.removeAttribute('data-highlighted');
                output.textContent = formatted;
                hljs.highlightElement(output);

                status.textContent = 'Valides XML';
                status.className = 'status valid visible';
                setTimeout(() => {
                    status.classList.remove('visible');
                }, 2000);
            } catch (error) {
                status.textContent = `Fehler: ${error.message}`;
                status.className = 'status invalid visible';
                output.textContent = '';
                output.removeAttribute('data-highlighted');
                hljs.highlightElement(output);
            }
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                inputArea.value = e.target.result;
                validateAndFormatXML();
            };
            reader.readAsText(file);
        }

        function clearInput() {
            document.getElementById('input').value = '';
            document.getElementById('output').textContent = '';
            document.getElementById('status').className = 'status';
        }

        function copyToClipboard() {
            const output = document.getElementById('output');
            const copyButton = document.getElementById('copyButton');
            
            if (!output.textContent) return;

            navigator.clipboard.writeText(output.textContent).then(() => {
                copyButton.classList.add('success');
                const originalIcon = copyButton.innerHTML;
                copyButton.innerHTML = `
                    <svg viewBox="0 0 24 24">
                        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                    </svg>
                `;
                
                setTimeout(() => {
                    copyButton.classList.remove('success');
                    copyButton.innerHTML = originalIcon;
                }, 2000);
            });
        }

        // Dark Mode Detection
        const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        darkModeMediaQuery.addListener((e) => {
            document.documentElement.classList.toggle('dark-mode', e.matches);
        });
    </script>
</body>
</html> 
